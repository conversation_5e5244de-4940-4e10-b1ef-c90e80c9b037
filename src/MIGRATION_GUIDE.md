# Configuration Migration Guide

## Overview
This guide helps migrate from direct environment variable usage to the new centralized configuration system.

## Step 1: Update Imports

Replace:
```python
import os
from dotenv import load_dotenv
```

With:
```python
from config import load_settings, get_settings
```

## Step 2: Initialize Settings

At the start of your application lifecycle:
```python
# Load settings once
settings = load_settings()

# Validate configuration
validation_result = settings.validate()
if validation_result.health == ConfigHealth.CRITICAL:
    print("Critical configuration errors:", validation_result.errors)
    sys.exit(1)
```

## Step 3: Replace Environment Variable Access

### Before:
```python
if os.getenv("USE_RERANKING", "false") == "true":
    # Enable reranking
```

### After:
```python
if settings.strategies.use_reranking:
    # Enable reranking
```

## Step 4: Update Function Signatures

### Before:
```python
def process_documents():
    api_key = os.getenv("OPENAI_API_KEY")
    # ...
```

### After:
```python
def process_documents(settings: Settings):
    api_key = settings.ai.openai_api_key.get_secret_value()
    # ...
```

## Step 5: Handle Optional Components

```python
# Check if Neo4j is configured
if settings.neo4j:
    uri = settings.neo4j.uri
    user = settings.neo4j.user
    password = settings.neo4j.password.get_secret_value()
else:
    # Handle Neo4j not configured
```

## Step 6: Use Health Monitoring

```python
# Get configuration health report
health_report = settings.get_health_report()
print(f"Configuration health: {health_report['health']}")

if health_report['warnings']:
    print("Configuration warnings:")
    for warning in health_report['warnings']:
        print(f"  - {warning}")
```

## Testing the Migration

1. Create a test configuration:
```python
test_config = {
    "SUPABASE_URL": "https://test.supabase.co",
    "SUPABASE_SERVICE_KEY": "test-key",
    "OPENAI_API_KEY": "test-openai-key",
    "USE_RERANKING": "true"
}

test_settings = Settings.from_dict(test_config)
```

2. Verify all features work with new configuration
3. Check performance metrics remain the same
4. Ensure graceful degradation works when components are missing

## Benefits After Migration

- ✅ Type-safe configuration access
- ✅ Centralized validation
- ✅ Better error messages
- ✅ Easy testing with mock configurations
- ✅ Health monitoring and reporting
- ✅ Graceful degradation support
