"""Hybrid configuration management combining best practices from all approaches."""

import os
import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from functools import lru_cache
from pathlib import Path
from typing import Dict, Any, List, Optional, Protocol, Type, TypeVar
import logging

from pydantic import BaseModel, Field, SecretStr, validator
from pydantic_settings import BaseSettings as PydanticBaseSettings

logger = logging.getLogger(__name__)

T = TypeVar('T', bound='BaseSettings')

# === From Implementation 2: Type Safety and Protocols ===

class ConfigSource(Protocol):
    """Protocol for configuration sources."""
    
    def get(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get configuration value by key."""
        ...

class EnvironmentConfigSource:
    """Environment variables configuration source."""
    
    def get(self, key: str, default: Optional[str] = None) -> Optional[str]:
        return os.environ.get(key, default)

# === From Implementation 3: Health Monitoring ===

class ConfigHealth(Enum):
    """Configuration health status."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    CRITICAL = "critical"

@dataclass
class ConfigValidationResult:
    """Result of configuration validation."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    health: ConfigHealth = ConfigHealth.HEALTHY

# === From Implementation 2: Pydantic Models ===

class BaseSettings(BaseModel):
    """Base settings with validation."""
    
    class Config:
        validate_assignment = True
        use_enum_values = True

class DatabaseSettings(BaseSettings):
    """Database configuration."""
    url: str = Field(..., description="Supabase database URL")
    key: SecretStr = Field(..., description="Supabase service key")
    
    @validator('url')
    def validate_url(cls, v: str) -> str:
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('Database URL must start with http:// or https://')
        return v

class Neo4jSettings(PydanticBaseSettings):
    """Neo4j database configuration."""
    uri: Optional[str] = Field(None, description="Neo4j database URI")
    user: Optional[str] = Field(None, description="Neo4j username")
    password: Optional[SecretStr] = Field(None, description="Neo4j password")

    @validator('uri')
    def validate_uri(cls, v: Optional[str]) -> Optional[str]:
        if v and not v.startswith(('bolt://', 'neo4j://', 'neo4j+s://', 'neo4j+ssc://')):
            raise ValueError('Neo4j URI must start with bolt://, neo4j://, neo4j+s://, or neo4j+ssc://')
        return v

    class Config:
        env_prefix = "NEO4J_"

class AISettings(BaseSettings):
    """AI service configuration."""
    openai_api_key: SecretStr = Field(..., description="OpenAI API key")
    embedding_model: str = Field(default="text-embedding-ada-002")
    model_choice: Optional[str] = Field(None, description="Model choice for AI operations")

# === From Implementation 1: Performance Settings ===

class PerformanceSettings(BaseSettings):
    """Performance-specific configuration."""
    max_concurrent_crawls: int = Field(default=10, ge=1, le=100)
    connection_pool_size: int = Field(default=20, ge=1, le=100)
    cache_ttl_seconds: int = Field(default=3600, ge=60)
    lazy_load_strategies: bool = Field(default=True)

class StrategySettings(PydanticBaseSettings):
    """Strategy configuration."""
    # Feature flags from environment
    use_contextual_embeddings: bool = Field(default=False, description="Enable contextual embeddings")
    use_hybrid_search: bool = Field(default=False, description="Enable hybrid search")
    use_reranking: bool = Field(default=False, description="Enable reranking")
    use_agentic_rag: bool = Field(default=False, description="Enable agentic RAG")
    use_knowledge_graph: bool = Field(default=False, description="Enable knowledge graph")

    # Strategy-specific settings
    contextual_embeddings: Dict[str, Any] = Field(default_factory=dict)
    hybrid_search: Dict[str, Any] = Field(default_factory=dict)
    reranking: Dict[str, Any] = Field(default_factory=dict)

    @validator('use_contextual_embeddings', 'use_hybrid_search', 'use_reranking', 'use_agentic_rag', 'use_knowledge_graph', pre=True)
    def parse_bool_env_vars(cls, v):
        """Parse boolean environment variables with support for empty strings."""
        if isinstance(v, str):
            if v == '':
                return False
            return v.lower() in ('true', '1', 'yes', 'on')
        return v

    class Config:
        env_prefix = ""  # No prefix for environment variables

class ServerSettings(PydanticBaseSettings):
    """Server configuration."""
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8051, ge=1, le=65535, description="Server port")

    class Config:
        env_prefix = ""  # No prefix for HOST and PORT

# === From Implementation 4: Plugin Support ===

class ConfigPlugin(ABC):
    """Base class for configuration plugins."""
    
    @abstractmethod
    def transform(self, settings: 'Settings') -> None:
        """Transform settings after loading."""
        pass

# === Hybrid Settings Class ===

class Settings:
    """Hybrid settings combining all best practices."""
    
    def __init__(self, source: Optional[ConfigSource] = None):
        """Initialize settings with optional config source."""
        self._source = source or EnvironmentConfigSource()
        self._validation_result: Optional[ConfigValidationResult] = None
        self._plugins: List[ConfigPlugin] = []
        
        # From Implementation 1: Lazy loading cache
        self._lazy_cache: Dict[str, Any] = {}
        
        # From Implementation 3: Fallback values
        self._fallbacks: Dict[str, Any] = {}
        
        # Load settings
        self._load_settings()
    
    def _load_settings(self) -> None:
        """Load settings with fallback mechanism."""
        try:
            # Try loading from source
            self.database = self._load_database_settings()
            self.neo4j = self._load_neo4j_settings()
            self.ai = self._load_ai_settings()
            self.performance = self._load_performance_settings()
            self.strategies = self._load_strategy_settings()
            self.server = self._load_server_settings()
        except Exception as e:
            logger.error(f"Error loading settings: {e}")
            # From Implementation 3: Use safe defaults
            self._load_defaults()
    
    def _load_database_settings(self) -> DatabaseSettings:
        """Load database settings with validation."""
        try:
            return DatabaseSettings(
                url=self._source.get("SUPABASE_URL", ""),
                key=self._source.get("SUPABASE_SERVICE_KEY", "")
            )
        except Exception as e:
            logger.warning(f"Database settings error: {e}")
            self._fallbacks["database"] = "Using in-memory storage"
            raise
    
    def _load_neo4j_settings(self) -> Optional[Neo4jSettings]:
        """Load Neo4j settings if available."""
        try:
            uri = self._source.get("NEO4J_URI")
            if not uri:
                return None
            
            return Neo4jSettings(
                uri=uri,
                user=self._source.get("NEO4J_USER"),
                password=self._source.get("NEO4J_PASSWORD")
            )
        except Exception as e:
            logger.warning(f"Neo4j settings error: {e}")
            self._fallbacks["neo4j"] = "Knowledge graph features disabled"
            return None
    
    def _load_ai_settings(self) -> AISettings:
        """Load AI settings with validation."""
        try:
            return AISettings(
                openai_api_key=self._source.get("OPENAI_API_KEY", ""),
                model_choice=self._source.get("MODEL_CHOICE")
            )
        except Exception as e:
            logger.warning(f"AI settings error: {e}")
            self._fallbacks["ai"] = "AI features disabled"
            raise
    
    def _load_performance_settings(self) -> PerformanceSettings:
        """Load performance settings."""
        return PerformanceSettings(
            max_concurrent_crawls=int(self._source.get("MAX_CONCURRENT_CRAWLS", "10")),
            connection_pool_size=int(self._source.get("CONNECTION_POOL_SIZE", "20")),
            lazy_load_strategies=self._source.get("LAZY_LOAD_STRATEGIES", "true").lower() == "true"
        )
    
    def _load_strategy_settings(self) -> StrategySettings:
        """Load strategy settings."""
        return StrategySettings(
            use_contextual_embeddings=self._source.get("USE_CONTEXTUAL_EMBEDDINGS", "false").lower() == "true",
            use_hybrid_search=self._source.get("USE_HYBRID_SEARCH", "false").lower() == "true",
            use_reranking=self._source.get("USE_RERANKING", "false").lower() == "true",
            use_agentic_rag=self._source.get("USE_AGENTIC_RAG", "false").lower() == "true",
            use_knowledge_graph=self._source.get("USE_KNOWLEDGE_GRAPH", "false").lower() == "true"
        )
    
    def _load_server_settings(self) -> ServerSettings:
        """Load server settings."""
        return ServerSettings(
            host=self._source.get("HOST", "0.0.0.0"),
            port=int(self._source.get("PORT", "8051"))
        )
    
    def _load_defaults(self) -> None:
        """Load default configuration for graceful degradation."""
        self.database = None
        self.neo4j = None
        self.ai = None
        self.performance = PerformanceSettings()
        self.strategies = StrategySettings()
        self.server = ServerSettings()
    
    # === From Implementation 1: Performance Optimization ===
    
    @lru_cache(maxsize=128)
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with caching."""
        # Support dot notation
        parts = key.split('.')
        obj = self
        
        for part in parts:
            if hasattr(obj, part):
                obj = getattr(obj, part)
            else:
                return default
        
        return obj
    
    async def initialize_async(self) -> None:
        """Async initialization for better startup performance."""
        # From Implementation 1: Parallel initialization
        tasks = []
        
        if self.performance.lazy_load_strategies:
            # Lazy load strategies in background
            tasks.append(self._preload_strategies())
        
        if tasks:
            await asyncio.gather(*tasks)
    
    async def _preload_strategies(self) -> None:
        """Preload strategy configurations asynchronously."""
        # Simulate async loading
        await asyncio.sleep(0)  # Yield control
        logger.info("Strategies preloaded")
    
    # === From Implementation 3: Validation and Health ===
    
    def validate(self) -> ConfigValidationResult:
        """Comprehensive validation with health status."""
        result = ConfigValidationResult(is_valid=True)

        # Check critical components - these should cause validation failure
        if not self.database or not self.database.url:
            result.errors.append("Database URL is required")
            result.is_valid = False
            result.health = ConfigHealth.CRITICAL

        if not self.ai or not self.ai.openai_api_key.get_secret_value():
            result.errors.append("OpenAI API key is required")
            result.is_valid = False
            result.health = ConfigHealth.CRITICAL
        
        # Check Neo4j if knowledge graph is enabled
        if self.strategies.use_knowledge_graph and not self.neo4j:
            result.warnings.append("Knowledge graph enabled but Neo4j not configured")
            result.health = ConfigHealth.DEGRADED
        
        # Check for fallbacks
        if self._fallbacks:
            result.warnings.extend(self._fallbacks.values())
        
        # Validate performance settings
        if self.performance.max_concurrent_crawls > 50:
            result.warnings.append("High concurrent crawl limit may impact performance")
        
        self._validation_result = result
        return result
    
    def get_health_report(self) -> Dict[str, Any]:
        """Get configuration health report."""
        if not self._validation_result:
            self.validate()
        
        return {
            "health": self._validation_result.health.value,
            "is_valid": self._validation_result.is_valid,
            "errors": self._validation_result.errors,
            "warnings": self._validation_result.warnings,
            "fallbacks_active": len(self._fallbacks),
        }
    
    # === From Implementation 4: Plugin Support ===
    
    def register_plugin(self, plugin: ConfigPlugin) -> None:
        """Register a configuration plugin."""
        self._plugins.append(plugin)
        plugin.transform(self)
    
    # === From Implementation 2: Factory Pattern ===
    
    @classmethod
    def from_dict(cls, config: Dict[str, str]) -> 'Settings':
        """Create settings from dictionary."""
        class DictConfigSource:
            def __init__(self, data: Dict[str, str]):
                self.data = data
            
            def get(self, key: str, default: Optional[str] = None) -> Optional[str]:
                return self.data.get(key, default)
        
        return cls(DictConfigSource(config))

# === Singleton Instance ===

_settings_instance: Optional[Settings] = None

def load_settings() -> Settings:
    """Load settings with singleton pattern."""
    global _settings_instance
    
    if _settings_instance is None:
        _settings_instance = Settings()
        
        # Validate on first load
        validation = _settings_instance.validate()
        if validation.health == ConfigHealth.CRITICAL:
            raise ValueError(f"Critical configuration errors: {validation.errors}")
        elif validation.warnings:
            logger.warning(f"Configuration warnings: {validation.warnings}")
    
    return _settings_instance

def get_settings() -> Settings:
    """Get current settings instance."""
    if _settings_instance is None:
        return load_settings()
    return _settings_instance