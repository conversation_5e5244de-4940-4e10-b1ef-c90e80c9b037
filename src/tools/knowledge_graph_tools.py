"""
Knowledge Graph tools for MCP server.

This module contains MCP tools related to knowledge graph functionality including
Neo4j querying, repository analysis, and AI script hallucination detection.
"""

import json
import os
from typing import Optional, Any, Dict, List
from mcp.server.fastmcp import FastMCP


def register_knowledge_graph_tools(app: FastMCP, context: Any) -> None:
    """Register all knowledge graph-related MCP tools with the FastMCP app."""
    
    @app.tool()
    async def check_ai_script_hallucinations(script_path: str) -> str:
        """
        Check AI-generated Python scripts for hallucinations using knowledge graph validation.
        
        This tool analyzes Python scripts to detect potential hallucinations by comparing
        against known APIs and patterns stored in the knowledge graph.
        
        Args:
            script_path: Path to the Python script file to analyze
            
        Returns:
            JSON string with hallucination detection results
        """
        try:
            # Validate script path
            if not os.path.exists(script_path):
                return json.dumps({
                    "success": False,
                    "script_path": script_path,
                    "error": "Script file not found"
                }, indent=2)
            
            if not script_path.endswith('.py'):
                return json.dumps({
                    "success": False,
                    "script_path": script_path,
                    "error": "File is not a Python script"
                }, indent=2)
            
            # Get AI script analyzer from context
            script_analyzer = getattr(context, 'script_analyzer', None)
            if not script_analyzer:
                return json.dumps({
                    "success": False,
                    "error": "AI script analyzer not available in context"
                }, indent=2)
            
            # Read script content
            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    script_content = f.read()
            except Exception as e:
                return json.dumps({
                    "success": False,
                    "script_path": script_path,
                    "error": f"Failed to read script file: {str(e)}"
                }, indent=2)
            
            # Analyze script for hallucinations
            analysis_result = await script_analyzer.analyze_script(script_content, script_path)
            
            if analysis_result['success']:
                return json.dumps({
                    "success": True,
                    "script_path": script_path,
                    "analysis": analysis_result['analysis'],
                    "hallucinations_detected": analysis_result['analysis']['total_issues'] > 0,
                    "total_issues": analysis_result['analysis']['total_issues'],
                    "issue_categories": analysis_result['analysis']['issues_by_category']
                }, indent=2)
            else:
                return json.dumps({
                    "success": False,
                    "script_path": script_path,
                    "error": analysis_result.get('error', 'Analysis failed')
                }, indent=2)
                
        except Exception as e:
            return json.dumps({
                "success": False,
                "script_path": script_path,
                "error": f"Hallucination check failed: {str(e)}"
            }, indent=2)
    
    @app.tool()
    async def query_knowledge_graph(command: str) -> str:
        """
        Query and explore the Neo4j knowledge graph with natural language commands.
        
        Supported commands:
        - repos: List all repositories
        - explore <repo_name>: Explore repository structure
        - classes <repo_name>: List classes in repository
        - class <class_name> [repo]: Get class details
        - method <method_name> [class] [repo]: Get method details
        - query <cypher>: Execute custom Cypher query
        
        Args:
            command: Natural language command to execute
            
        Returns:
            JSON string with query results
        """
        try:
            # Get knowledge graph validator from context
            knowledge_validator = getattr(context, 'knowledge_validator', None)
            if not knowledge_validator:
                return json.dumps({
                    "success": False,
                    "error": "Knowledge graph validator not available in context"
                }, indent=2)
            
            # Parse and route command
            command = command.strip().lower()
            command_parts = command.split()
            
            if not command_parts:
                return json.dumps({
                    "success": False,
                    "error": "Empty command provided"
                }, indent=2)
            
            main_command = command_parts[0]
            
            # Route to appropriate handler
            if main_command == "repos":
                return await _handle_repos_command(knowledge_validator)
            elif main_command == "explore":
                if len(command_parts) < 2:
                    return json.dumps({
                        "success": False,
                        "error": "Repository name required for explore command"
                    }, indent=2)
                return await _handle_explore_command(knowledge_validator, command_parts[1])
            elif main_command == "classes":
                if len(command_parts) < 2:
                    return json.dumps({
                        "success": False,
                        "error": "Repository name required for classes command"
                    }, indent=2)
                return await _handle_classes_command(knowledge_validator, command_parts[1])
            elif main_command == "class":
                if len(command_parts) < 2:
                    return json.dumps({
                        "success": False,
                        "error": "Class name required for class command"
                    }, indent=2)
                repo_name = command_parts[2] if len(command_parts) > 2 else None
                return await _handle_class_command(knowledge_validator, command_parts[1], repo_name)
            elif main_command == "method":
                if len(command_parts) < 2:
                    return json.dumps({
                        "success": False,
                        "error": "Method name required for method command"
                    }, indent=2)
                class_name = command_parts[2] if len(command_parts) > 2 else None
                repo_name = command_parts[3] if len(command_parts) > 3 else None
                return await _handle_method_command(knowledge_validator, command_parts[1], class_name, repo_name)
            elif main_command == "query":
                if len(command_parts) < 2:
                    return json.dumps({
                        "success": False,
                        "error": "Cypher query required for query command"
                    }, indent=2)
                cypher_query = " ".join(command_parts[1:])
                return await _handle_query_command(knowledge_validator, cypher_query)
            else:
                return json.dumps({
                    "success": False,
                    "error": f"Unknown command: {main_command}",
                    "supported_commands": ["repos", "explore", "classes", "class", "method", "query"]
                }, indent=2)
                
        except Exception as e:
            return json.dumps({
                "success": False,
                "command": command,
                "error": f"Knowledge graph query failed: {str(e)}"
            }, indent=2)
    
    @app.tool()
    async def parse_github_repository(repo_url: str) -> str:
        """
        Parse a GitHub repository and extract its structure into the Neo4j knowledge graph.
        
        This tool clones the repository, analyzes its code structure, and creates
        nodes and relationships in the knowledge graph for classes, methods, and dependencies.
        
        Args:
            repo_url: GitHub repository URL to parse
            
        Returns:
            JSON string with parsing results and statistics
        """
        try:
            # Validate GitHub URL
            if not _is_valid_github_url(repo_url):
                return json.dumps({
                    "success": False,
                    "repo_url": repo_url,
                    "error": "Invalid GitHub repository URL"
                }, indent=2)
            
            # Get repository extractor from context
            repo_extractor = getattr(context, 'repo_extractor', None)
            if not repo_extractor:
                return json.dumps({
                    "success": False,
                    "error": "Repository extractor not available in context"
                }, indent=2)
            
            # Extract repository information
            extraction_result = await repo_extractor.extract_repository(repo_url)
            
            if extraction_result['success']:
                stats = extraction_result['statistics']
                return json.dumps({
                    "success": True,
                    "repo_url": repo_url,
                    "repository_name": extraction_result['repository_name'],
                    "extraction_statistics": {
                        "total_files": stats.get('total_files', 0),
                        "python_files": stats.get('python_files', 0),
                        "classes_extracted": stats.get('classes_extracted', 0),
                        "methods_extracted": stats.get('methods_extracted', 0),
                        "functions_extracted": stats.get('functions_extracted', 0),
                        "relationships_created": stats.get('relationships_created', 0)
                    },
                    "processing_time": extraction_result.get('processing_time', 0),
                    "nodes_created": extraction_result.get('nodes_created', 0),
                    "relationships_created": extraction_result.get('relationships_created', 0)
                }, indent=2)
            else:
                return json.dumps({
                    "success": False,
                    "repo_url": repo_url,
                    "error": extraction_result.get('error', 'Repository extraction failed')
                }, indent=2)
                
        except Exception as e:
            return json.dumps({
                "success": False,
                "repo_url": repo_url,
                "error": f"Repository parsing failed: {str(e)}"
            }, indent=2)


# Helper functions for knowledge graph commands

async def _handle_repos_command(knowledge_validator: Any) -> str:
    """Handle the 'repos' command to list all repositories."""
    try:
        repos = await knowledge_validator.list_repositories()
        return json.dumps({
            "success": True,
            "command": "repos",
            "repositories_count": len(repos),
            "repositories": repos
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "command": "repos",
            "error": str(e)
        }, indent=2)


async def _handle_explore_command(knowledge_validator: Any, repo_name: str) -> str:
    """Handle the 'explore' command to explore repository structure."""
    try:
        structure = await knowledge_validator.explore_repository(repo_name)
        return json.dumps({
            "success": True,
            "command": "explore",
            "repository": repo_name,
            "structure": structure
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "command": "explore",
            "repository": repo_name,
            "error": str(e)
        }, indent=2)


async def _handle_classes_command(knowledge_validator: Any, repo_name: str) -> str:
    """Handle the 'classes' command to list classes in repository."""
    try:
        classes = await knowledge_validator.list_classes(repo_name)
        return json.dumps({
            "success": True,
            "command": "classes",
            "repository": repo_name,
            "classes_count": len(classes),
            "classes": classes
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "command": "classes",
            "repository": repo_name,
            "error": str(e)
        }, indent=2)


async def _handle_class_command(knowledge_validator: Any, class_name: str, repo_name: Optional[str] = None) -> str:
    """Handle the 'class' command to get class details."""
    try:
        class_details = await knowledge_validator.get_class_details(class_name, repo_name)
        return json.dumps({
            "success": True,
            "command": "class",
            "class_name": class_name,
            "repository": repo_name,
            "class_details": class_details
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "command": "class",
            "class_name": class_name,
            "repository": repo_name,
            "error": str(e)
        }, indent=2)


async def _handle_method_command(knowledge_validator: Any, method_name: str, class_name: Optional[str] = None, repo_name: Optional[str] = None) -> str:
    """Handle the 'method' command to get method details."""
    try:
        method_details = await knowledge_validator.get_method_details(method_name, class_name, repo_name)
        return json.dumps({
            "success": True,
            "command": "method",
            "method_name": method_name,
            "class_name": class_name,
            "repository": repo_name,
            "method_details": method_details
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "command": "method",
            "method_name": method_name,
            "class_name": class_name,
            "repository": repo_name,
            "error": str(e)
        }, indent=2)


async def _handle_query_command(knowledge_validator: Any, cypher_query: str) -> str:
    """Handle the 'query' command to execute custom Cypher queries."""
    try:
        results = await knowledge_validator.execute_cypher(cypher_query)
        return json.dumps({
            "success": True,
            "command": "query",
            "cypher_query": cypher_query,
            "results_count": len(results) if isinstance(results, list) else 1,
            "results": results
        }, indent=2)
    except Exception as e:
        return json.dumps({
            "success": False,
            "command": "query",
            "cypher_query": cypher_query,
            "error": str(e)
        }, indent=2)


def _is_valid_github_url(url: str) -> bool:
    """Validate that a URL is a valid GitHub repository URL."""
    import re
    
    # GitHub URL pattern
    github_pattern = r'^https://github\.com/[\w\-\.]+/[\w\-\.]+/?$'
    
    return bool(re.match(github_pattern, url))


def _validate_script_path(script_path: str) -> Dict[str, Any]:
    """Validate script path and return validation results."""
    if not script_path:
        return {"valid": False, "error": "Script path is required"}
    
    if not os.path.exists(script_path):
        return {"valid": False, "error": f"Script file not found: {script_path}"}
    
    if not script_path.endswith('.py'):
        return {"valid": False, "error": "File must be a Python script (.py)"}
    
    if not os.path.isfile(script_path):
        return {"valid": False, "error": "Path must point to a file, not a directory"}
    
    try:
        # Check if file is readable
        with open(script_path, 'r') as f:
            f.read(1)  # Try to read first character
        return {"valid": True}
    except Exception as e:
        return {"valid": False, "error": f"Cannot read script file: {str(e)}"}


def _validate_neo4j_connection(knowledge_validator: Any) -> Dict[str, Any]:
    """Validate Neo4j connection through knowledge validator."""
    try:
        if not knowledge_validator:
            return {"connected": False, "error": "Knowledge validator not available"}
        
        # Try a simple validation query
        result = knowledge_validator.test_connection()
        return {"connected": True, "info": result}
    except Exception as e:
        return {"connected": False, "error": f"Neo4j connection failed: {str(e)}"}


def _format_neo4j_error(error: Exception) -> str:
    """Format Neo4j errors for user-friendly display."""
    error_str = str(error)
    
    if "authentication" in error_str.lower():
        return "Neo4j authentication failed. Please check your credentials."
    elif "connection" in error_str.lower():
        return "Cannot connect to Neo4j database. Please check the connection settings."
    elif "syntax" in error_str.lower():
        return f"Cypher query syntax error: {error_str}"
    else:
        return f"Neo4j error: {error_str}"