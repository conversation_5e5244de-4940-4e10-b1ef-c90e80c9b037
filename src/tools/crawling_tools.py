"""
Crawling tools for MCP server.

This module contains MCP tools related to web crawling functionality,
extracted from the monolithic implementation for better organization.
"""

import json
from typing import Optional, Any
from mcp.server.fastmcp import FastMCP
from src.crawlers import CrawlerManager
from src.utils import add_documents_to_supabase_simple, update_source_info, extract_source_summary


def register_crawling_tools(app: FastMCP, context: Any) -> None:
    """Register all crawling-related MCP tools with the FastMCP app."""
    
    @app.tool()
    async def crawl_single_page(url: str) -> str:
        """
        Crawl a single web page and store its content in Supabase.
        
        Args:
            url: The URL to crawl
            
        Returns:
            JSON string with crawl results
        """
        try:
            # Get crawler manager from context
            crawler_manager = getattr(context, 'crawler_manager', None)
            if not crawler_manager:
                return json.dumps({
                    "success": False,
                    "error": "Crawler manager not available in context"
                }, indent=2)
            
            # Perform single page crawl
            result = await crawler_manager.crawl(url)
            
            if not result.success:
                return result.to_json()
            
            # Store content in Supabase
            try:
                supabase_client = getattr(context, 'supabase_client', None)
                if not supabase_client:
                    return json.dumps({
                        "success": False,
                        "error": "Supabase client not available"
                    }, indent=2)
                
                # Prepare documents for storage
                documents = [{
                    'content': result.content,
                    'metadata': {
                        'url': url,
                        'title': result.metadata.get('title', '') if result.metadata else '',
                        'crawl_type': 'single_page'
                    }
                }]
                
                # Create source record first (required for foreign key constraint)
                from urllib.parse import urlparse
                parsed_url = urlparse(url)
                source_id = parsed_url.netloc or parsed_url.path
                update_source_info(supabase_client, source_id, "Single page crawl", len(result.content.split()))

                # Store in Supabase
                doc_ids = add_documents_to_supabase_simple(supabase_client, documents)

                if doc_ids:
                    
                    return json.dumps({
                        "success": True,
                        "url": url,
                        "documents_stored": len(doc_ids),
                        "document_ids": doc_ids,
                        "content_length": len(result.content),
                        "title": result.metadata.get('title', '') if result.metadata else ''
                    }, indent=2)
                else:
                    return json.dumps({
                        "success": False,
                        "url": url,
                        "error": "Failed to store documents in Supabase"
                    }, indent=2)
                    
            except Exception as e:
                return json.dumps({
                    "success": False,
                    "url": url,
                    "error": f"Database storage error: {str(e)}"
                }, indent=2)
                
        except Exception as e:
            return json.dumps({
                "success": False,
                "url": url,
                "error": f"Crawling error: {str(e)}"
            }, indent=2)
    
    @app.tool()
    async def smart_crawl_url(
        url: str, 
        max_depth: int = 3, 
        max_concurrent: int = 10,
        chunk_size: int = 5000
    ) -> str:
        """
        Intelligently crawl URLs based on type detection (sitemap, text file, webpage).
        
        This tool automatically detects the URL type and applies the appropriate crawling strategy:
        - Text files (.txt, .md): Direct content extraction
        - Sitemaps (sitemap.xml): Parse and batch crawl URLs
        - Webpages: Recursive internal link following
        
        Args:
            url: The URL to crawl
            max_depth: Maximum depth for recursive crawling (default: 3)
            max_concurrent: Maximum concurrent crawls (default: 10)
            chunk_size: Size of content chunks for processing (default: 5000)
            
        Returns:
            JSON string with comprehensive crawl results
        """
        try:
            # Get crawler manager from context
            crawler_manager = getattr(context, 'crawler_manager', None)
            if not crawler_manager:
                return json.dumps({
                    "success": False,
                    "error": "Crawler manager not available in context"
                }, indent=2)
            
            # Get URL routing information
            routing_info = crawler_manager.get_url_routing_info(url)
            detected_type = routing_info['detected_type']
            
            crawl_results = []
            crawl_type = detected_type
            
            # Route to appropriate crawling strategy
            if detected_type == 'text_file':
                # Handle text files
                result = await crawler_manager.crawl(url)
                if result.success:
                    crawl_results = [result]
                else:
                    return result.to_json()
                    
            elif detected_type == 'sitemap':
                # Handle sitemaps - parse and batch crawl
                sitemap_result = await crawler_manager.crawl(url)
                if sitemap_result.success and sitemap_result.urls_found:
                    # Batch crawl sitemap URLs
                    batch_results = await crawler_manager.crawl_batch(
                        sitemap_result.urls_found,
                        max_concurrent=max_concurrent
                    )
                    crawl_results = [r for r in batch_results if r.success]
                else:
                    return sitemap_result.to_json()
                    
            else:
                # Handle regular webpages with recursive crawling
                results = await crawler_manager.crawl_recursive(
                    [url],
                    max_depth=max_depth,
                    max_concurrent=max_concurrent
                )
                crawl_results = [r for r in results if r.success]
            
            if not crawl_results:
                return json.dumps({
                    "success": False,
                    "url": url,
                    "error": "No content successfully crawled"
                }, indent=2)
            
            # Process and store results in Supabase
            try:
                supabase_client = getattr(context, 'supabase_client', None)
                if not supabase_client:
                    return json.dumps({
                        "success": False,
                        "error": "Supabase client not available"
                    }, indent=2)
                
                # Prepare documents for storage with chunking
                all_documents = []
                total_content_length = 0
                
                for result in crawl_results:
                    if result.content:
                        # Chunk the content if it's too large
                        chunks = _smart_chunk_markdown(result.content, chunk_size)
                        
                        for i, chunk in enumerate(chunks):
                            section_info = _extract_section_info(chunk)
                            
                            doc = {
                                'content': chunk,
                                'metadata': {
                                    'url': result.url,
                                    'title': result.metadata.get('title', '') if result.metadata else '',
                                    'crawl_type': crawl_type,
                                    'chunk_index': i,
                                    'total_chunks': len(chunks),
                                    'section_type': section_info.get('section_type', 'content'),
                                    'has_code': section_info.get('has_code', False)
                                }
                            }
                            all_documents.append(doc)
                            total_content_length += len(chunk)
                
                # Store in Supabase
                doc_ids = add_documents_to_supabase_simple(supabase_client, all_documents)

                if doc_ids:
                    # Update source information
                    # Get content for summary (use first result's content)
                    summary_content = crawl_results[0].content if crawl_results else ""
                    source_summary = extract_source_summary(url, summary_content)
                    update_source_info(
                        supabase_client,
                        url,
                        source_summary,
                        len(doc_ids)
                    )
                    
                    return json.dumps({
                        "success": True,
                        "url": url,
                        "crawl_type": crawl_type,
                        "pages_crawled": len(crawl_results),
                        "documents_stored": len(doc_ids),
                        "total_content_length": total_content_length,
                        "routing_info": routing_info,
                        "crawled_urls": [r.url for r in crawl_results[:10]]  # Show first 10 URLs
                    }, indent=2)
                else:
                    return json.dumps({
                        "success": False,
                        "url": url,
                        "error": "Failed to store documents in Supabase"
                    }, indent=2)
                    
            except Exception as e:
                return json.dumps({
                    "success": False,
                    "url": url,
                    "error": f"Database storage error: {str(e)}"
                }, indent=2)
                
        except Exception as e:
            return json.dumps({
                "success": False,
                "url": url,
                "error": f"Smart crawling error: {str(e)}"
            }, indent=2)


def _smart_chunk_markdown(text: str, chunk_size: int = 5000) -> list[str]:
    """
    Intelligently chunk markdown content while preserving structure.
    
    Args:
        text: The markdown text to chunk
        chunk_size: Target size for each chunk
        
    Returns:
        List of text chunks
    """
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    current_chunk = ""
    
    # Split by double newlines (paragraphs/sections)
    sections = text.split('\n\n')
    
    for section in sections:
        # If adding this section would exceed chunk size, start new chunk
        if current_chunk and len(current_chunk) + len(section) + 2 > chunk_size:
            chunks.append(current_chunk.strip())
            current_chunk = section
        else:
            if current_chunk:
                current_chunk += '\n\n' + section
            else:
                current_chunk = section
        
        # If even a single section is too large, split it
        if len(current_chunk) > chunk_size * 1.5:
            # Try to split by code blocks first
            if '```' in current_chunk:
                code_parts = current_chunk.split('```')
                temp_chunk = ""
                for i, part in enumerate(code_parts):
                    if i % 2 == 0:  # Text part
                        if temp_chunk and len(temp_chunk) + len(part) > chunk_size:
                            chunks.append(temp_chunk.strip())
                            temp_chunk = part
                        else:
                            temp_chunk += part
                    else:  # Code part
                        code_block = '```' + part + '```'
                        if temp_chunk and len(temp_chunk) + len(code_block) > chunk_size:
                            chunks.append(temp_chunk.strip())
                            temp_chunk = code_block
                        else:
                            temp_chunk += code_block
                current_chunk = temp_chunk
            else:
                # Split by sentences if no code blocks
                sentences = current_chunk.split('. ')
                temp_chunk = ""
                for sentence in sentences:
                    if temp_chunk and len(temp_chunk) + len(sentence) + 2 > chunk_size:
                        chunks.append(temp_chunk.strip())
                        temp_chunk = sentence
                    else:
                        if temp_chunk:
                            temp_chunk += '. ' + sentence
                        else:
                            temp_chunk = sentence
                current_chunk = temp_chunk
    
    # Add the last chunk
    if current_chunk.strip():
        chunks.append(current_chunk.strip())
    
    return chunks


def _extract_section_info(chunk: str) -> dict:
    """
    Extract metadata about a content chunk.
    
    Args:
        chunk: The text chunk to analyze
        
    Returns:
        Dictionary with section information
    """
    info = {
        'section_type': 'content',
        'has_code': False,
        'has_headers': False,
        'has_lists': False
    }
    
    # Check for code blocks
    if '```' in chunk or '`' in chunk:
        info['has_code'] = True
        info['section_type'] = 'code'
    
    # Check for headers
    if chunk.strip().startswith('#'):
        info['has_headers'] = True
        info['section_type'] = 'header'
    
    # Check for lists
    if any(line.strip().startswith(('- ', '* ', '+ ')) for line in chunk.split('\n')):
        info['has_lists'] = True
        if info['section_type'] == 'content':
            info['section_type'] = 'list'
    
    return info