"""
RAG (Retrieval-Augmented Generation) tools for MCP server.

This module contains MCP tools related to RAG functionality including document search,
code example retrieval, and knowledge base querying.
"""

import json
from typing import Optional, Any, List, Dict
from mcp.server.fastmcp import FastMCP
from src.utils import (
    search_documents, 
    search_code_examples, 
    extract_code_blocks,
    generate_code_example_summary,
    add_code_examples_to_supabase
)


def register_rag_tools(app: FastMCP, context: Any) -> None:
    """Register all RAG-related MCP tools with the FastMCP app."""
    
    @app.tool()
    async def get_available_sources() -> str:
        """
        Get all available sources from the sources table.
        
        Returns:
            JSON string with list of available sources and their metadata
        """
        try:
            supabase_client = getattr(context, 'supabase_client', None)
            if not supabase_client:
                return json.dumps({
                    "success": False,
                    "error": "Supabase client not available"
                }, indent=2)
            
            # Query the sources table
            response = supabase_client.table('sources').select('*').execute()
            
            if response.data:
                # Format the sources data
                sources = []
                for source in response.data:
                    source_info = {
                        'url': source.get('url', ''),
                        'title': source.get('title', ''),
                        'document_count': source.get('document_count', 0),
                        'last_updated': source.get('updated_at', ''),
                        'summary': source.get('summary', ''),
                        'content_length': source.get('content_length', 0)
                    }
                    sources.append(source_info)
                
                return json.dumps({
                    "success": True,
                    "sources_count": len(sources),
                    "sources": sources
                }, indent=2)
            else:
                return json.dumps({
                    "success": True,
                    "sources_count": 0,
                    "sources": [],
                    "message": "No sources found in database"
                }, indent=2)
                
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"Failed to get sources: {str(e)}"
            }, indent=2)
    
    @app.tool()
    async def perform_rag_query(
        query: str, 
        source: Optional[str] = None, 
        match_count: int = 5
    ) -> str:
        """
        Perform RAG queries on stored content using semantic search.
        
        This tool searches through stored documents using vector similarity and
        applies various enhancement strategies based on configuration.
        
        Args:
            query: The search query
            source: Optional source URL to limit search to specific source
            match_count: Number of results to return (default: 5)
            
        Returns:
            JSON string with search results and applied strategies
        """
        try:
            supabase_client = getattr(context, 'supabase_client', None)
            if not supabase_client:
                return json.dumps({
                    "success": False,
                    "error": "Supabase client not available"
                }, indent=2)
            
            # Get strategy manager from context for potential enhancements
            strategy_manager = getattr(context, 'strategy_manager', None)
            
            # Enhanced query if contextual embeddings strategy is available
            enhanced_query = query
            if strategy_manager and hasattr(strategy_manager, 'contextual_embeddings'):
                try:
                    enhanced_query = await strategy_manager.contextual_embeddings.enhance_query(query)
                except Exception as e:
                    # Fall back to original query if enhancement fails
                    enhanced_query = query
            
            # Perform the search
            results = await search_documents(
                supabase_client, 
                enhanced_query, 
                source_filter=source,
                match_count=match_count
            )
            
            if not results:
                return json.dumps({
                    "success": True,
                    "query": query,
                    "enhanced_query": enhanced_query if enhanced_query != query else None,
                    "source_filter": source,
                    "results_count": 0,
                    "results": [],
                    "message": "No relevant documents found"
                }, indent=2)
            
            # Apply reranking if available
            reranked_results = results
            reranking_applied = False
            
            reranking_model = getattr(context, 'reranking_model', None)
            if reranking_model and strategy_manager and hasattr(strategy_manager, 'reranking'):
                try:
                    reranked_results = await strategy_manager.reranking.rerank_results(
                        reranking_model, query, results
                    )
                    reranking_applied = True
                except Exception as e:
                    # Fall back to original results if reranking fails
                    reranked_results = results
            
            # Apply hybrid search enhancements if available
            hybrid_enhanced = False
            if strategy_manager and hasattr(strategy_manager, 'hybrid_search'):
                try:
                    # Hybrid search combines vector and keyword search
                    hybrid_results = await strategy_manager.hybrid_search.search(
                        supabase_client, query, source_filter=source, match_count=match_count
                    )
                    if hybrid_results:
                        reranked_results = hybrid_results
                        hybrid_enhanced = True
                except Exception as e:
                    # Fall back to existing results if hybrid search fails
                    pass
            
            # Format results for response
            formatted_results = []
            for i, result in enumerate(reranked_results[:match_count]):
                formatted_result = {
                    'rank': i + 1,
                    'content': result.get('content', ''),
                    'metadata': result.get('metadata', {}),
                    'similarity_score': result.get('similarity', 0.0),
                    'url': result.get('metadata', {}).get('url', ''),
                    'title': result.get('metadata', {}).get('title', ''),
                    'section_type': result.get('metadata', {}).get('section_type', 'content')
                }
                formatted_results.append(formatted_result)
            
            # Prepare response with strategy information
            response_data = {
                "success": True,
                "query": query,
                "source_filter": source,
                "results_count": len(formatted_results),
                "results": formatted_results,
                "strategies_applied": {
                    "contextual_embeddings": enhanced_query != query,
                    "reranking": reranking_applied,
                    "hybrid_search": hybrid_enhanced
                }
            }
            
            if enhanced_query != query:
                response_data["enhanced_query"] = enhanced_query
            
            return json.dumps(response_data, indent=2)
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "query": query,
                "error": f"RAG query failed: {str(e)}"
            }, indent=2)
    
    @app.tool()
    async def search_code_examples(
        query: str, 
        source_id: Optional[str] = None, 
        match_count: int = 5
    ) -> str:
        """
        Search for code examples relevant to the query.
        
        This tool searches through extracted code examples and applies agentic RAG
        strategies for enhanced code understanding and analysis.
        
        Args:
            query: The search query for code examples
            source_id: Optional source ID to limit search scope
            match_count: Number of code examples to return (default: 5)
            
        Returns:
            JSON string with relevant code examples and analysis
        """
        try:
            supabase_client = getattr(context, 'supabase_client', None)
            if not supabase_client:
                return json.dumps({
                    "success": False,
                    "error": "Supabase client not available"
                }, indent=2)
            
            # Get strategy manager for agentic RAG capabilities
            strategy_manager = getattr(context, 'strategy_manager', None)
            
            # Check if agentic RAG strategy is available for code analysis
            agentic_analysis = False
            enhanced_examples = []
            
            # Search for code examples
            code_results = await search_code_examples(
                supabase_client,
                query,
                source_id=source_id,
                match_count=match_count
            )
            
            if not code_results:
                return json.dumps({
                    "success": True,
                    "query": query,
                    "source_id": source_id,
                    "results_count": 0,
                    "code_examples": [],
                    "message": "No relevant code examples found"
                }, indent=2)
            
            # Apply agentic RAG analysis if available
            if strategy_manager and hasattr(strategy_manager, 'agentic_rag'):
                try:
                    for result in code_results:
                        # Extract and analyze code from the result
                        code_content = result.get('code', result.get('content', ''))
                        
                        # Apply agentic analysis
                        analysis = await strategy_manager.agentic_rag.analyze_code(
                            code_content, query
                        )
                        
                        enhanced_example = {
                            'id': result.get('id'),
                            'code': code_content,
                            'language': result.get('language', 'unknown'),
                            'summary': result.get('summary', ''),
                            'context': result.get('context', ''),
                            'source_url': result.get('source_url', ''),
                            'similarity_score': result.get('similarity', 0.0),
                            'agentic_analysis': analysis
                        }
                        enhanced_examples.append(enhanced_example)
                    
                    agentic_analysis = True
                    
                except Exception as e:
                    # Fall back to basic code examples if agentic analysis fails
                    enhanced_examples = [
                        {
                            'id': result.get('id'),
                            'code': result.get('code', result.get('content', '')),
                            'language': result.get('language', 'unknown'),
                            'summary': result.get('summary', ''),
                            'context': result.get('context', ''),
                            'source_url': result.get('source_url', ''),
                            'similarity_score': result.get('similarity', 0.0)
                        }
                        for result in code_results
                    ]
            else:
                # Basic code examples without agentic analysis
                enhanced_examples = [
                    {
                        'id': result.get('id'),
                        'code': result.get('code', result.get('content', '')),
                        'language': result.get('language', 'unknown'),
                        'summary': result.get('summary', ''),
                        'context': result.get('context', ''),
                        'source_url': result.get('source_url', ''),
                        'similarity_score': result.get('similarity', 0.0)
                    }
                    for result in code_results
                ]
            
            return json.dumps({
                "success": True,
                "query": query,
                "source_id": source_id,
                "results_count": len(enhanced_examples),
                "code_examples": enhanced_examples,
                "agentic_analysis_applied": agentic_analysis
            }, indent=2)
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "query": query,
                "error": f"Code search failed: {str(e)}"
            }, indent=2)


async def _extract_and_store_code_examples(context: Any, crawl_results: List[Dict]) -> Dict[str, Any]:
    """
    Helper function to extract code examples from crawl results and store them.
    
    Args:
        context: Application context with supabase client
        crawl_results: List of crawl result dictionaries
        
    Returns:
        Dictionary with extraction results
    """
    try:
        supabase_client = getattr(context, 'supabase_client', None)
        if not supabase_client:
            return {"success": False, "error": "Supabase client not available"}
        
        total_code_examples = 0
        processed_urls = []
        
        for result in crawl_results:
            if result.get('success') and result.get('content'):
                # Extract code blocks from content
                code_blocks = extract_code_blocks(result['content'])
                
                if code_blocks:
                    # Generate summaries for code examples
                    code_examples = []
                    for code in code_blocks:
                        summary = await generate_code_example_summary(code)
                        code_examples.append({
                            'code': code['code'],
                            'language': code.get('language', 'unknown'),
                            'summary': summary,
                            'context': code.get('context', ''),
                            'source_url': result.get('url', '')
                        })
                    
                    # Store code examples in Supabase
                    stored_count = await add_code_examples_to_supabase(
                        supabase_client, code_examples
                    )
                    
                    total_code_examples += stored_count
                    processed_urls.append(result.get('url', ''))
        
        return {
            "success": True,
            "total_code_examples": total_code_examples,
            "processed_urls": processed_urls
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Code extraction failed: {str(e)}"
        }