"""
Tools module for MCP server functionality.

This module provides domain-specific MCP tools organized by functionality:
- Crawling tools: Web crawling and content extraction
- RAG tools: Retrieval-augmented generation and search
- Knowledge graph tools: Neo4j integration and analysis
"""

from typing import Any
from mcp.server.fastmcp import FastMCP
from .crawling_tools import register_crawling_tools
from .rag_tools import register_rag_tools
from .knowledge_graph_tools import register_knowledge_graph_tools


def register_all_tools(app: FastMCP, context: Any) -> None:
    """
    Register all MCP tools with the FastMCP application.
    
    This function registers tools from all domain-specific modules:
    - 2 crawling tools (crawl_single_page, smart_crawl_url)
    - 3 RAG tools (get_available_sources, perform_rag_query, search_code_examples)
    - 3 knowledge graph tools (check_ai_script_hallucinations, query_knowledge_graph, parse_github_repository)
    
    Args:
        app: FastMCP application instance
        context: Application context containing shared resources
    """
    # Register tools by domain
    register_crawling_tools(app, context)
    register_rag_tools(app, context)
    register_knowledge_graph_tools(app, context)


__all__ = [
    'register_all_tools',
    'register_crawling_tools',
    'register_rag_tools', 
    'register_knowledge_graph_tools'
]