"""Contextual embeddings strategy for enhanced semantic understanding."""

from typing import List, Dict, Any
import logging

from .base import IndexingStrategy

logger = logging.getLogger(__name__)


class ContextualEmbeddingsStrategy(IndexingStrategy):
    """
    Strategy that enhances embeddings with contextual information.
    
    This strategy modifies the text before embedding generation to include
    context about the source, purpose, and domain, which can improve
    semantic similarity matching.
    """
    
    def get_name(self) -> str:
        """Get strategy name."""
        return "contextual_embeddings"
    
    def is_enabled(self) -> bool:
        """Check if contextual embeddings are enabled."""
        return self.settings.strategies.use_contextual_embeddings
    
    async def process_crawl_results(
        self, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Process crawl results to add contextual information before embedding.
        
        Args:
            results: Raw crawl results
            **kwargs: Additional parameters (source_url, domain, etc.)
            
        Returns:
            List[Dict]: Enhanced results with contextual information
        """
        if not self.is_enabled():
            self.logger.debug("Contextual embeddings disabled, skipping processing")
            return results
        
        self.logger.info(f"Processing {len(results)} results with contextual embeddings")
        
        enhanced_results = []
        source_url = kwargs.get('source_url', 'unknown')
        domain = kwargs.get('domain', self._extract_domain(source_url))
        
        for result in results:
            enhanced_result = result.copy()
            
            # Add contextual prefix to content
            original_content = result.get('content', '')
            if original_content:
                contextual_content = self._add_context(
                    content=original_content,
                    source_url=source_url,
                    domain=domain,
                    metadata=result.get('metadata', {})
                )
                enhanced_result['content'] = contextual_content
                
                # Store original content for reference
                enhanced_result['original_content'] = original_content
                
                # Add context metadata
                enhanced_result.setdefault('metadata', {}).update({
                    'contextual_enhancement': True,
                    'source_domain': domain,
                    'context_strategy': 'contextual_embeddings'
                })
            
            enhanced_results.append(enhanced_result)
        
        self.logger.info(f"Enhanced {len(enhanced_results)} results with contextual information")
        return enhanced_results
    
    def _add_context(
        self, 
        content: str, 
        source_url: str, 
        domain: str, 
        metadata: Dict[str, Any]
    ) -> str:
        """
        Add contextual information to content before embedding.
        
        Args:
            content: Original content
            source_url: Source URL
            domain: Domain name
            metadata: Additional metadata
            
        Returns:
            str: Content with contextual prefix
        """
        # Create contextual prefix based on content type and source
        context_parts = []
        
        # Add domain context
        if domain and domain != 'unknown':
            context_parts.append(f"Source: {domain}")
        
        # Add content type context
        content_type = metadata.get('content_type', 'webpage')
        if content_type:
            context_parts.append(f"Type: {content_type}")
        
        # Add page title if available
        title = metadata.get('title')
        if title:
            context_parts.append(f"Title: {title}")
        
        # Add section context if available
        section = metadata.get('section')
        if section:
            context_parts.append(f"Section: {section}")
        
        # Build contextual prefix
        if context_parts:
            context_prefix = "Context: " + " | ".join(context_parts) + "\n\n"
            return context_prefix + content
        
        return content
    
    def _extract_domain(self, url: str) -> str:
        """
        Extract domain from URL for contextual information.
        
        Args:
            url: Source URL
            
        Returns:
            str: Domain name or 'unknown'
        """
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            domain = parsed.netloc
            
            # Clean up common prefixes
            if domain.startswith('www.'):
                domain = domain[4:]
            
            return domain or 'unknown'
        except Exception as e:
            self.logger.warning(f"Failed to extract domain from {url}: {e}")
            return 'unknown'
    
    async def enhance_search(
        self, 
        query: str, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Enhance search by creating contextual query for better matching.
        
        Args:
            query: Original search query
            results: Search results (not modified by this strategy)
            **kwargs: Additional parameters
            
        Returns:
            List[Dict]: Unmodified results (enhancement happens at query level)
        """
        if not self.is_enabled():
            return results
        
        # This strategy primarily works at indexing time,
        # but we can log the query enhancement for monitoring
        enhanced_query = self._enhance_query(query, **kwargs)
        self.logger.debug(f"Enhanced query: {enhanced_query}")
        
        return results
    
    def _enhance_query(self, query: str, **kwargs) -> str:
        """
        Create contextual query for better semantic matching.
        
        Args:
            query: Original query
            **kwargs: Context parameters
            
        Returns:
            str: Enhanced query with context
        """
        # Add context to query for better semantic matching
        context_parts = []
        
        # Add search intent
        context_parts.append("Find information about:")
        
        # Add domain context if specified
        domain = kwargs.get('domain')
        if domain:
            context_parts.append(f"from {domain}")
        
        # Build enhanced query
        if context_parts:
            context_prefix = " ".join(context_parts) + " "
            return context_prefix + query
        
        return query
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get strategy metadata."""
        metadata = super().get_metadata()
        metadata.update({
            "description": "Enhances content with contextual information before embedding",
            "type": "indexing",
            "affects": ["embedding_generation", "semantic_search"]
        })
        return metadata