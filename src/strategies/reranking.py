"""Reranking strategy using cross-encoder models for improved relevance."""

from typing import List, Dict, Any, Optional, Tuple
import logging
import numpy as np

from .base import SearchStrategy

logger = logging.getLogger(__name__)


class RerankingStrategy(SearchStrategy):
    """
    Strategy that reranks search results using a cross-encoder model.
    
    Cross-encoders can better understand query-document relevance compared
    to simple similarity metrics, leading to more accurate ranking of results.
    """
    
    def get_name(self) -> str:
        """Get strategy name."""
        return "reranking"
    
    def is_enabled(self) -> bool:
        """Check if reranking is enabled and model is available."""
        return (
            self.settings.strategies.use_reranking and 
            self.context.reranking_model is not None
        )
    
    async def enhance_search(
        self, 
        query: str, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Rerank search results using cross-encoder model.
        
        Args:
            query: The search query
            results: Initial search results to rerank
            **kwargs: Additional parameters (top_k, batch_size, etc.)
            
        Returns:
            List[Dict]: Reranked results with updated scores
        """
        if not self.is_enabled():
            self.logger.debug("Reranking disabled or model not available")
            return results
        
        if not results:
            self.logger.debug("No results to rerank")
            return results
        
        self.logger.info(f"Reranking {len(results)} results for query: '{query}'")
        
        try:
            # Get parameters
            top_k = kwargs.get('top_k', len(results))
            batch_size = kwargs.get('batch_size', 32)
            
            # Prepare query-document pairs
            query_doc_pairs = []
            for result in results:
                content = result.get('content', '')
                if content:
                    # Truncate content for the cross-encoder (models have token limits)
                    truncated_content = self._truncate_content(content, max_tokens=512)
                    query_doc_pairs.append([query, truncated_content])
            
            if not query_doc_pairs:
                self.logger.warning("No valid content found for reranking")
                return results
            
            # Get cross-encoder scores
            rerank_scores = await self._compute_rerank_scores(
                query_doc_pairs, 
                batch_size=batch_size
            )
            
            # Apply new scores to results
            reranked_results = self._apply_rerank_scores(results, rerank_scores)
            
            # Sort by new scores and limit to top_k
            reranked_results.sort(key=lambda x: x.get('rerank_score', 0), reverse=True)
            final_results = reranked_results[:top_k]
            
            self.logger.info(f"Reranking complete, returning top {len(final_results)} results")
            
            # Add metadata about reranking
            for i, result in enumerate(final_results):
                result.setdefault('metadata', {}).update({
                    'reranked': True,
                    'rerank_position': i + 1,
                    'original_position': results.index(result) + 1 if result in results else -1
                })
            
            return final_results
            
        except Exception as e:
            self.logger.error(f"Reranking failed: {e}")
            self.logger.warning("Falling back to original ranking")
            return results
    
    async def _compute_rerank_scores(
        self, 
        query_doc_pairs: List[List[str]], 
        batch_size: int = 32
    ) -> List[float]:
        """
        Compute reranking scores using the cross-encoder model.
        
        Args:
            query_doc_pairs: List of [query, document] pairs
            batch_size: Batch size for processing
            
        Returns:
            List[float]: Relevance scores for each pair
        """
        if not self.context.reranking_model:
            raise ValueError("Reranking model not available")
        
        all_scores = []
        
        # Process in batches to manage memory
        for i in range(0, len(query_doc_pairs), batch_size):
            batch = query_doc_pairs[i:i + batch_size]
            
            try:
                # Compute scores for this batch
                batch_scores = self.context.reranking_model.predict(batch)
                
                # Handle different output formats
                if isinstance(batch_scores, np.ndarray):
                    batch_scores = batch_scores.tolist()
                
                # Ensure we have a list of scores
                if not isinstance(batch_scores, list):
                    batch_scores = [batch_scores]
                
                all_scores.extend(batch_scores)
                
            except Exception as e:
                self.logger.error(f"Error processing batch {i//batch_size + 1}: {e}")
                # Use fallback scores for failed batch
                fallback_scores = [0.0] * len(batch)
                all_scores.extend(fallback_scores)
        
        return all_scores
    
    def _apply_rerank_scores(
        self, 
        results: List[Dict[str, Any]], 
        scores: List[float]
    ) -> List[Dict[str, Any]]:
        """
        Apply reranking scores to search results.
        
        Args:
            results: Original search results
            scores: Reranking scores
            
        Returns:
            List[Dict]: Results with reranking scores applied
        """
        reranked_results = []
        
        for i, (result, score) in enumerate(zip(results, scores)):
            enhanced_result = result.copy()
            
            # Store original similarity score
            original_score = result.get('similarity', 0.0)
            enhanced_result['original_similarity'] = original_score
            
            # Apply rerank score
            enhanced_result['rerank_score'] = float(score)
            
            # Update main similarity score with rerank score
            enhanced_result['similarity'] = float(score)
            
            # Add reranking metadata
            enhanced_result.setdefault('metadata', {}).update({
                'rerank_applied': True,
                'original_rank': i + 1,
                'score_improvement': float(score) - original_score
            })
            
            reranked_results.append(enhanced_result)
        
        return reranked_results
    
    def _truncate_content(self, content: str, max_tokens: int = 512) -> str:
        """
        Truncate content to fit within token limits of cross-encoder.
        
        Args:
            content: Original content
            max_tokens: Maximum number of tokens (approximate)
            
        Returns:
            str: Truncated content
        """
        # Simple approximation: ~4 characters per token for English text
        max_chars = max_tokens * 4
        
        if len(content) <= max_chars:
            return content
        
        # Truncate and try to end at a sentence boundary
        truncated = content[:max_chars]
        
        # Find the last complete sentence
        last_period = truncated.rfind('.')
        last_exclamation = truncated.rfind('!')
        last_question = truncated.rfind('?')
        
        last_sentence_end = max(last_period, last_exclamation, last_question)
        
        if last_sentence_end > max_chars * 0.7:  # If we find a sentence end in the last 30%
            return truncated[:last_sentence_end + 1]
        else:
            # Otherwise, truncate at word boundary
            last_space = truncated.rfind(' ')
            if last_space > max_chars * 0.8:  # If we find a space in the last 20%
                return truncated[:last_space]
            else:
                return truncated + "..."
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the reranking model.
        
        Returns:
            Dict: Model information
        """
        if not self.context.reranking_model:
            return {"model": "none", "available": False}
        
        try:
            model_info = {
                "available": True,
                "model_name": getattr(self.context.reranking_model, 'model_name', 'unknown'),
                "model_type": "cross-encoder"
            }
            
            # Try to get additional model details
            if hasattr(self.context.reranking_model, 'config'):
                config = self.context.reranking_model.config
                model_info.update({
                    "max_seq_length": getattr(config, 'max_seq_length', 'unknown'),
                    "num_labels": getattr(config, 'num_labels', 'unknown')
                })
            
            return model_info
            
        except Exception as e:
            self.logger.error(f"Error getting model info: {e}")
            return {"model": "error", "available": False, "error": str(e)}
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get strategy metadata."""
        metadata = super().get_metadata()
        metadata.update({
            "description": "Reranks search results using cross-encoder models",
            "type": "search",
            "affects": ["result_ranking", "relevance_scoring"],
            "model_info": self.get_model_info()
        })
        return metadata