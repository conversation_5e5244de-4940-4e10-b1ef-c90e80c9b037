"""Strategy manager for coordinating RAG strategies."""

from typing import List, Dict, Any, Optional, TYPE_CHECKING
import logging

from .base import RAGStrategy
from .contextual_embeddings import ContextualEmbeddingsStrategy
from .hybrid_search import HybridSearchStrategy
from .reranking import RerankingStrategy
from .agentic_rag import AgenticRAGStrategy

if TYPE_CHECKING:
    from config import Settings
    from core.context import Crawl4AIContext

logger = logging.getLogger(__name__)


class StrategyManager:
    """
    Coordinates multiple RAG strategies for enhanced search and processing.
    
    The manager:
    1. Initializes enabled strategies based on settings
    2. Orchestrates strategy execution for crawl processing and search enhancement
    3. Handles strategy lifecycle and error recovery
    4. Provides monitoring and debugging information
    """
    
    def __init__(self, settings: 'Settings', context: 'Crawl4AIContext'):
        """
        Initialize strategy manager with settings and context.
        
        Args:
            settings: Application settings
            context: Application context
        """
        self.settings = settings
        self.context = context
        self.logger = logging.getLogger(__name__)
        
        # Strategy registry
        self._strategies: List[RAGStrategy] = []
        self._strategy_map: Dict[str, RAGStrategy] = {}
        
        # Initialize strategies
        self._initialize_strategies()
    
    def _initialize_strategies(self) -> None:
        """Initialize all available strategies based on settings."""
        self.logger.info("Initializing RAG strategies...")
        
        # Available strategy classes
        strategy_classes = [
            ContextualEmbeddingsStrategy,
            HybridSearchStrategy,
            RerankingStrategy,
            AgenticRAGStrategy
        ]
        
        # Initialize each strategy
        for strategy_class in strategy_classes:
            try:
                strategy = strategy_class(self.settings, self.context)
                
                # Only register enabled strategies
                if strategy.is_enabled():
                    self._strategies.append(strategy)
                    self._strategy_map[strategy.get_name()] = strategy
                    self.logger.info(f"✓ Enabled strategy: {strategy.get_name()}")
                else:
                    self.logger.debug(f"Strategy disabled: {strategy.get_name()}")
                    
            except Exception as e:
                self.logger.error(f"Failed to initialize {strategy_class.__name__}: {e}")
        
        self.logger.info(f"Initialized {len(self._strategies)} RAG strategies")
    
    async def initialize_all(self) -> None:
        """Initialize all registered strategies."""
        self.logger.info("Initializing strategy resources...")
        
        for strategy in self._strategies:
            try:
                await strategy.initialize()
                self.logger.debug(f"Initialized strategy: {strategy.get_name()}")
            except Exception as e:
                self.logger.error(f"Failed to initialize strategy {strategy.get_name()}: {e}")
    
    async def process_crawl_results(
        self, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Process crawl results through all enabled indexing strategies.
        
        Args:
            results: Raw crawl results
            **kwargs: Additional parameters
            
        Returns:
            List[Dict]: Enhanced crawl results
        """
        if not results:
            return results
        
        self.logger.info(f"Processing {len(results)} crawl results through {len(self._strategies)} strategies")
        
        processed_results = results
        
        # Apply each strategy in sequence
        for strategy in self._strategies:
            try:
                if hasattr(strategy, 'process_crawl_results'):
                    processed_results = await strategy.process_crawl_results(
                        processed_results, 
                        **kwargs
                    )
                    self.logger.debug(f"Applied crawl processing: {strategy.get_name()}")
            except Exception as e:
                self.logger.error(f"Strategy {strategy.get_name()} failed during crawl processing: {e}")
                # Continue with other strategies
        
        self.logger.info(f"Crawl processing complete, processed {len(processed_results)} results")
        return processed_results
    
    async def enhance_search(
        self, 
        query: str, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Enhance search results through all enabled search strategies.
        
        Args:
            query: Search query
            results: Initial search results
            **kwargs: Additional parameters
            
        Returns:
            List[Dict]: Enhanced search results
        """
        if not results:
            return results
        
        self.logger.info(f"Enhancing search for '{query}' with {len(self._strategies)} strategies")
        
        enhanced_results = results
        
        # Apply each strategy in sequence
        for strategy in self._strategies:
            try:
                if hasattr(strategy, 'enhance_search'):
                    enhanced_results = await strategy.enhance_search(
                        query, 
                        enhanced_results, 
                        **kwargs
                    )
                    self.logger.debug(f"Applied search enhancement: {strategy.get_name()}")
            except Exception as e:
                self.logger.error(f"Strategy {strategy.get_name()} failed during search enhancement: {e}")
                # Continue with other strategies
        
        self.logger.info(f"Search enhancement complete, returning {len(enhanced_results)} results")
        return enhanced_results
    
    def get_strategy(self, name: str) -> Optional[RAGStrategy]:
        """
        Get a specific strategy by name.
        
        Args:
            name: Strategy name
            
        Returns:
            RAGStrategy instance or None if not found
        """
        return self._strategy_map.get(name)
    
    def get_enabled_strategies(self) -> List[str]:
        """
        Get list of enabled strategy names.
        
        Returns:
            List[str]: Enabled strategy names
        """
        return [strategy.get_name() for strategy in self._strategies]
    
    def get_strategy_metadata(self) -> Dict[str, Any]:
        """
        Get metadata for all strategies.
        
        Returns:
            Dict: Strategy metadata organized by name
        """
        metadata = {}
        
        for strategy in self._strategies:
            try:
                metadata[strategy.get_name()] = strategy.get_metadata()
            except Exception as e:
                metadata[strategy.get_name()] = {
                    "error": str(e),
                    "enabled": False
                }
        
        return metadata
    
    def get_status_report(self) -> Dict[str, Any]:
        """
        Get comprehensive status report for all strategies.
        
        Returns:
            Dict: Status report with strategy information
        """
        return {
            "total_strategies": len(self._strategies),
            "enabled_strategies": self.get_enabled_strategies(),
            "strategy_metadata": self.get_strategy_metadata(),
            "settings": {
                "contextual_embeddings": self.settings.strategies.use_contextual_embeddings,
                "hybrid_search": self.settings.strategies.use_hybrid_search,
                "reranking": self.settings.strategies.use_reranking,
                "agentic_rag": self.settings.strategies.use_agentic_rag,
                "knowledge_graph": self.settings.strategies.use_knowledge_graph
            }
        }
    
    async def close(self) -> None:
        """Clean up all strategy resources."""
        self.logger.info("Closing strategy manager...")
        
        for strategy in self._strategies:
            try:
                await strategy.cleanup()
                self.logger.debug(f"Cleaned up strategy: {strategy.get_name()}")
            except Exception as e:
                self.logger.error(f"Failed to cleanup strategy {strategy.get_name()}: {e}")
        
        self._strategies.clear()
        self._strategy_map.clear()
        
        self.logger.info("Strategy manager closed")