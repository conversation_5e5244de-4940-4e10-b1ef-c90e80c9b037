"""Hybrid search strategy combining BM25 and vector similarity."""

from typing import List, Dict, Any, Optional
import logging

from .base import SearchStrategy

logger = logging.getLogger(__name__)


class HybridSearchStrategy(SearchStrategy):
    """
    Strategy that combines traditional keyword search (BM25) with vector similarity.
    
    This strategy enhances search by:
    1. Performing both keyword and semantic search
    2. Combining results with weighted scoring
    3. Improving recall by catching both exact matches and semantic similarity
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._bm25_weight = 0.5  # Weight for BM25 scores
        self._vector_weight = 0.5  # Weight for vector scores
    
    def get_name(self) -> str:
        """Get strategy name."""
        return "hybrid_search"
    
    def is_enabled(self) -> bool:
        """Check if hybrid search is enabled."""
        return self.settings.strategies.use_hybrid_search
    
    async def enhance_search(
        self, 
        query: str, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Enhance search results by combining keyword and semantic search.
        
        Args:
            query: The search query
            results: Initial vector search results
            **kwargs: Additional parameters (match_count, source_ids, etc.)
            
        Returns:
            List[Dict]: Enhanced results with hybrid scoring
        """
        if not self.is_enabled():
            self.logger.debug("Hybrid search disabled, returning vector results only")
            return results
        
        self.logger.info(f"Performing hybrid search for query: '{query}'")
        
        # Get parameters
        match_count = kwargs.get('match_count', 10)
        source_ids = kwargs.get('source_ids')
        match_threshold = kwargs.get('match_threshold', 0.7)
        
        try:
            # Perform keyword search using BM25
            keyword_results = await self._perform_keyword_search(
                query=query,
                match_count=match_count,
                source_ids=source_ids
            )
            
            # Combine and score results
            hybrid_results = self._combine_results(
                vector_results=results,
                keyword_results=keyword_results,
                query=query
            )
            
            # Filter by threshold if specified
            if match_threshold > 0:
                hybrid_results = [
                    result for result in hybrid_results 
                    if result.get('similarity', 0) >= match_threshold
                ]
            
            # Limit to requested count
            hybrid_results = hybrid_results[:match_count]
            
            self.logger.info(f"Hybrid search returned {len(hybrid_results)} results")
            return hybrid_results
            
        except Exception as e:
            self.logger.error(f"Hybrid search failed: {e}")
            self.logger.warning("Falling back to vector search only")
            return results
    
    async def _perform_keyword_search(
        self, 
        query: str, 
        match_count: int,
        source_ids: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform keyword search using BM25 or similar algorithm.
        
        Args:
            query: Search query
            match_count: Number of results to return
            source_ids: Optional source ID filter
            
        Returns:
            List[Dict]: Keyword search results with BM25 scores
        """
        # In a real implementation, this would use a BM25 search
        # For now, we'll simulate with a simple text matching approach
        
        if not self.context.supabase_client:
            self.logger.warning("No database client available for keyword search")
            return []
        
        try:
            # Simple keyword search implementation
            # In production, you'd want to use a proper full-text search engine
            query_terms = query.lower().split()
            
            # Build search query for Supabase
            search_query = self.context.supabase_client.table("documents")
            
            # Add source filter if specified
            if source_ids:
                search_query = search_query.in_("source_id", source_ids)
            
            # Perform text search (this is simplified - real BM25 would be more complex)
            response = search_query.select("*").limit(match_count * 2).execute()
            
            documents = response.data
            
            # Score documents based on keyword matches
            scored_results = []
            for doc in documents:
                content = doc.get('content', '').lower()
                bm25_score = self._calculate_bm25_score(content, query_terms)
                
                if bm25_score > 0:  # Only include documents with keyword matches
                    scored_results.append({
                        'id': doc.get('id'),
                        'content': doc.get('content'),
                        'metadata': doc.get('metadata', {}),
                        'source_id': doc.get('source_id'),
                        'bm25_score': bm25_score,
                        'search_type': 'keyword'
                    })
            
            # Sort by BM25 score and limit results
            scored_results.sort(key=lambda x: x['bm25_score'], reverse=True)
            return scored_results[:match_count]
            
        except Exception as e:
            self.logger.error(f"Keyword search failed: {e}")
            return []
    
    def _calculate_bm25_score(self, content: str, query_terms: List[str]) -> float:
        """
        Calculate a simplified BM25 score for keyword matching.
        
        Args:
            content: Document content
            query_terms: Query terms to match
            
        Returns:
            float: BM25-like score
        """
        # Simplified BM25 calculation
        # In production, use a proper BM25 implementation
        
        content_terms = content.split()
        content_length = len(content_terms)
        
        if content_length == 0:
            return 0.0
        
        score = 0.0
        for term in query_terms:
            # Count term frequency
            tf = content.count(term)
            if tf > 0:
                # Simplified TF component
                tf_component = tf / (tf + 1.2)  # k1 parameter
                
                # Length normalization (simplified)
                length_norm = 1.0 / (1.0 + 0.75 * (content_length / 100))  # assuming avg doc length = 100
                
                score += tf_component * length_norm
        
        return score
    
    def _combine_results(
        self, 
        vector_results: List[Dict[str, Any]], 
        keyword_results: List[Dict[str, Any]],
        query: str
    ) -> List[Dict[str, Any]]:
        """
        Combine vector and keyword search results with hybrid scoring.
        
        Args:
            vector_results: Results from vector similarity search
            keyword_results: Results from keyword search
            query: Original query
            
        Returns:
            List[Dict]: Combined results with hybrid scores
        """
        # Create lookup for vector results
        vector_lookup = {result.get('id'): result for result in vector_results}
        
        # Create lookup for keyword results
        keyword_lookup = {result.get('id'): result for result in keyword_results}
        
        # Get all unique document IDs
        all_ids = set(vector_lookup.keys()) | set(keyword_lookup.keys())
        
        combined_results = []
        
        for doc_id in all_ids:
            vector_result = vector_lookup.get(doc_id)
            keyword_result = keyword_lookup.get(doc_id)
            
            # Calculate hybrid score
            vector_score = vector_result.get('similarity', 0.0) if vector_result else 0.0
            keyword_score = keyword_result.get('bm25_score', 0.0) if keyword_result else 0.0
            
            # Normalize keyword score to 0-1 range (simplified)
            normalized_keyword_score = min(keyword_score / 2.0, 1.0)
            
            # Calculate weighted hybrid score
            hybrid_score = (
                self._vector_weight * vector_score + 
                self._bm25_weight * normalized_keyword_score
            )
            
            # Use the result with more complete information
            base_result = vector_result if vector_result else keyword_result
            
            if base_result:
                hybrid_result = base_result.copy()
                hybrid_result.update({
                    'similarity': hybrid_score,
                    'vector_score': vector_score,
                    'keyword_score': normalized_keyword_score,
                    'search_type': 'hybrid'
                })
                combined_results.append(hybrid_result)
        
        # Sort by hybrid score
        combined_results.sort(key=lambda x: x['similarity'], reverse=True)
        
        return combined_results
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get strategy metadata."""
        metadata = super().get_metadata()
        metadata.update({
            "description": "Combines keyword (BM25) and vector similarity search",
            "type": "search",
            "affects": ["search_results", "ranking"],
            "parameters": {
                "bm25_weight": self._bm25_weight,
                "vector_weight": self._vector_weight
            }
        })
        return metadata