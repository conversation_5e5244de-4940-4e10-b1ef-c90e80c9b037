"""Base types and interfaces for the modular RAG strategy system."""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from src.core.application_context import ApplicationContext


@dataclass
class RAGResult:
    """Represents a RAG query result."""
    title: str
    url: str
    content: str
    relevance_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    strategies_applied: List[str] = field(default_factory=list)


class RAGStrategy(ABC):
    """Abstract base class for RAG strategies."""
    
    @abstractmethod
    async def initialize(self):
        """Initialize the strategy (async setup)."""
        pass
    
    @abstractmethod
    async def process(self, query: str, results: List[RAGResult], context: 'ApplicationContext') -> List[RAGResult]:
        """Process RAG results through this strategy."""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """Clean up strategy resources."""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the name of this strategy."""
        pass
    
    @abstractmethod
    def is_enabled(self) -> bool:
        """Check if this strategy is enabled."""
        pass