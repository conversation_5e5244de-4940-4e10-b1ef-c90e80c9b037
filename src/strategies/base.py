"""Base classes and interfaces for RAG strategies."""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, TYPE_CHECKING
import logging

if TYPE_CHECKING:
    from ..core.context import Crawl4AIContext
    from ..config import Settings

logger = logging.getLogger(__name__)


class RAGStrategy(ABC):
    """
    Abstract base class for RAG strategies.
    
    Each strategy implements a specific enhancement to the RAG pipeline,
    such as contextual embeddings, hybrid search, or reranking.
    """
    
    def __init__(self, settings: 'Settings', context: 'Crawl4AIContext'):
        """
        Initialize the strategy with settings and context.
        
        Args:
            settings: Application settings
            context: Application context with resources
        """
        self.settings = settings
        self.context = context
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def is_enabled(self) -> bool:
        """
        Check if this strategy is enabled via settings.
        
        Returns:
            bool: True if the strategy should be used
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """
        Get the strategy name for identification.
        
        Returns:
            str: Unique strategy name
        """
        pass
    
    @abstractmethod
    async def enhance_search(
        self, 
        query: str, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Process and enhance search results.
        
        Args:
            query: The original search query
            results: Current search results to enhance
            **kwargs: Additional strategy-specific parameters
            
        Returns:
            List[Dict]: Enhanced search results
        """
        pass
    
    @abstractmethod
    async def process_crawl_results(
        self, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Process crawl results before indexing.
        
        Args:
            results: Raw crawl results to process
            **kwargs: Additional strategy-specific parameters
            
        Returns:
            List[Dict]: Processed crawl results
        """
        pass
    
    async def initialize(self) -> None:
        """
        Initialize strategy resources (optional).
        
        Called once when the strategy is first loaded.
        Can be used to load models, establish connections, etc.
        """
        pass
    
    async def cleanup(self) -> None:
        """
        Clean up strategy resources (optional).
        
        Called when the strategy is no longer needed.
        """
        pass
    
    def get_metadata(self) -> Dict[str, Any]:
        """
        Get strategy metadata for debugging/monitoring.
        
        Returns:
            Dict: Strategy metadata including name, enabled status, etc.
        """
        return {
            "name": self.get_name(),
            "enabled": self.is_enabled(),
            "class": self.__class__.__name__
        }


class SearchStrategy(RAGStrategy):
    """
    Base class for search-time strategies.
    
    These strategies enhance search queries and results,
    such as query expansion, result reranking, or filtering.
    """
    
    async def process_crawl_results(
        self, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Default implementation: no processing for search-only strategies.
        
        Args:
            results: Raw crawl results
            **kwargs: Additional parameters
            
        Returns:
            List[Dict]: Unmodified results
        """
        return results


class IndexingStrategy(RAGStrategy):
    """
    Base class for indexing-time strategies.
    
    These strategies process content before it's indexed,
    such as chunking, embedding generation, or content enhancement.
    """
    
    async def enhance_search(
        self, 
        query: str, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Default implementation: no enhancement for indexing-only strategies.
        
        Args:
            query: Search query
            results: Search results
            **kwargs: Additional parameters
            
        Returns:
            List[Dict]: Unmodified results
        """
        return results


class HybridStrategy(RAGStrategy):
    """
    Base class for strategies that work at both indexing and search time.
    
    These strategies need to implement both processing and enhancement.
    """
    pass