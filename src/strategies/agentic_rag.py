"""Agentic RAG strategy for intelligent code extraction and analysis."""

from typing import List, Dict, Any, Optional
import logging
import re

from .base import HybridStrategy

logger = logging.getLogger(__name__)


class AgenticRAGStrategy(HybridStrategy):
    """
    Strategy that implements agentic RAG for intelligent content processing.
    
    This strategy:
    1. Extracts and analyzes code examples from crawled content
    2. Generates summaries and explanations for code blocks
    3. Enhances search with code-specific understanding
    4. Provides structured code example retrieval
    """
    
    def get_name(self) -> str:
        """Get strategy name."""
        return "agentic_rag"
    
    def is_enabled(self) -> bool:
        """Check if agentic RAG is enabled."""
        return self.settings.strategies.use_agentic_rag
    
    async def process_crawl_results(
        self, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Process crawl results to extract and analyze code examples.
        
        Args:
            results: Raw crawl results
            **kwargs: Additional parameters (source_id, etc.)
            
        Returns:
            List[Dict]: Enhanced results with extracted code examples
        """
        if not self.is_enabled():
            self.logger.debug("Agentic RAG disabled, skipping code extraction")
            return results
        
        self.logger.info(f"Processing {len(results)} results with agentic RAG")
        
        enhanced_results = []
        source_id = kwargs.get('source_id', 'unknown')
        
        for result in results:
            enhanced_result = result.copy()
            content = result.get('content', '')
            
            if content:
                # Extract code blocks from content
                code_examples = self._extract_code_examples(content)
                
                if code_examples:
                    self.logger.info(f"Found {len(code_examples)} code examples")
                    
                    # Generate summaries for code examples
                    enriched_examples = await self._enrich_code_examples(
                        code_examples, 
                        context=content,
                        source_id=source_id
                    )
                    
                    # Add code examples to metadata
                    enhanced_result.setdefault('metadata', {}).update({
                        'code_examples': enriched_examples,
                        'has_code': True,
                        'code_count': len(enriched_examples),
                        'agentic_rag_processed': True
                    })
                    
                    # Optionally store code examples separately for indexing
                    if self.context.supabase_client:
                        await self._store_code_examples(enriched_examples, source_id)
            
            enhanced_results.append(enhanced_result)
        
        self.logger.info(f"Agentic RAG processing complete for {len(enhanced_results)} results")
        return enhanced_results
    
    async def enhance_search(
        self, 
        query: str, 
        results: List[Dict[str, Any]], 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Enhance search results by including relevant code examples.
        
        Args:
            query: Search query
            results: Current search results
            **kwargs: Additional parameters
            
        Returns:
            List[Dict]: Enhanced results with code example context
        """
        if not self.is_enabled():
            return results
        
        self.logger.info(f"Enhancing search with agentic RAG for query: '{query}'")
        
        # Check if this is a code-related query
        if self._is_code_query(query):
            # Search for relevant code examples
            code_results = await self._search_code_examples(query, **kwargs)
            
            if code_results:
                # Merge code examples with regular results
                enhanced_results = self._merge_with_code_results(results, code_results)
                self.logger.info(f"Enhanced results with {len(code_results)} code examples")
                return enhanced_results
        
        return results
    
    def _extract_code_examples(self, content: str) -> List[Dict[str, Any]]:
        """
        Extract code blocks from content using various patterns.
        
        Args:
            content: Text content to analyze
            
        Returns:
            List[Dict]: Extracted code examples with metadata
        """
        code_examples = []
        
        # Pattern 1: Markdown code blocks
        markdown_pattern = r'```(\w+)?\n(.*?)\n```'
        for match in re.finditer(markdown_pattern, content, re.DOTALL):
            language = match.group(1) or 'unknown'
            code = match.group(2).strip()
            
            if code and len(code) > 10:  # Filter out very short snippets
                code_examples.append({
                    'code': code,
                    'language': language,
                    'type': 'markdown_block',
                    'length': len(code)
                })
        
        # Pattern 2: HTML <code> blocks
        html_code_pattern = r'<code[^>]*>(.*?)</code>'
        for match in re.finditer(html_code_pattern, content, re.DOTALL):
            code = match.group(1).strip()
            
            # Clean HTML entities
            code = re.sub(r'&lt;', '<', code)
            code = re.sub(r'&gt;', '>', code)
            code = re.sub(r'&amp;', '&', code)
            
            if code and len(code) > 10:
                code_examples.append({
                    'code': code,
                    'language': 'unknown',
                    'type': 'html_code',
                    'length': len(code)
                })
        
        # Pattern 3: Indented code blocks (Python-style)
        lines = content.split('\n')
        current_block = []
        in_code_block = False
        
        for line in lines:
            if line.startswith('    ') or line.startswith('\t'):  # Indented line
                if not in_code_block:
                    in_code_block = True
                    current_block = [line.strip()]
                else:
                    current_block.append(line.strip())
            else:
                if in_code_block and current_block:
                    code = '\n'.join(current_block)
                    if len(code) > 20:  # Longer threshold for indented blocks
                        code_examples.append({
                            'code': code,
                            'language': 'python',  # Assumption for indented blocks
                            'type': 'indented_block',
                            'length': len(code)
                        })
                    current_block = []
                    in_code_block = False
        
        # Handle last block if file ends with code
        if in_code_block and current_block:
            code = '\n'.join(current_block)
            if len(code) > 20:
                code_examples.append({
                    'code': code,
                    'language': 'python',
                    'type': 'indented_block',
                    'length': len(code)
                })
        
        return code_examples
    
    async def _enrich_code_examples(
        self, 
        code_examples: List[Dict[str, Any]], 
        context: str,
        source_id: str
    ) -> List[Dict[str, Any]]:
        """
        Enrich code examples with AI-generated summaries and analysis.
        
        Args:
            code_examples: Raw code examples
            context: Surrounding content context
            source_id: Source identifier
            
        Returns:
            List[Dict]: Enriched code examples with summaries
        """
        enriched_examples = []
        
        for i, example in enumerate(code_examples):
            try:
                # Generate summary using AI
                summary = await self._generate_code_summary(
                    code=example['code'],
                    language=example['language'],
                    context=context
                )
                
                # Analyze code complexity and purpose
                analysis = self._analyze_code(example['code'], example['language'])
                
                enriched_example = example.copy()
                enriched_example.update({
                    'id': f"{source_id}_code_{i}",
                    'summary': summary,
                    'analysis': analysis,
                    'source_id': source_id,
                    'enriched': True
                })
                
                enriched_examples.append(enriched_example)
                
            except Exception as e:
                self.logger.error(f"Failed to enrich code example {i}: {e}")
                # Keep original example without enrichment
                enriched_examples.append(example)
        
        return enriched_examples
    
    async def _generate_code_summary(
        self, 
        code: str, 
        language: str, 
        context: str
    ) -> str:
        """
        Generate AI summary for code example.
        
        Args:
            code: Code content
            language: Programming language
            context: Surrounding context
            
        Returns:
            str: Generated summary
        """
        # This would integrate with the AI service to generate summaries
        # For now, return a placeholder
        
        # Simple heuristic-based summary
        lines = code.split('\n')
        line_count = len(lines)
        
        # Look for common patterns
        if 'def ' in code:
            func_matches = re.findall(r'def\s+(\w+)', code)
            if func_matches:
                return f"Defines {len(func_matches)} function(s): {', '.join(func_matches[:3])}"
        
        if 'class ' in code:
            class_matches = re.findall(r'class\s+(\w+)', code)
            if class_matches:
                return f"Defines class(es): {', '.join(class_matches[:2])}"
        
        if 'import ' in code or 'from ' in code:
            return f"{language} code with imports and {line_count} lines"
        
        return f"{language} code snippet with {line_count} lines"
    
    def _analyze_code(self, code: str, language: str) -> Dict[str, Any]:
        """
        Analyze code to extract structural information.
        
        Args:
            code: Code content
            language: Programming language
            
        Returns:
            Dict: Code analysis results
        """
        analysis = {
            'line_count': len(code.split('\n')),
            'char_count': len(code),
            'language': language,
            'complexity': 'simple'  # Would be calculated based on code structure
        }
        
        # Basic pattern detection
        if language.lower() in ['python', 'unknown']:
            analysis['functions'] = len(re.findall(r'def\s+\w+', code))
            analysis['classes'] = len(re.findall(r'class\s+\w+', code))
            analysis['imports'] = len(re.findall(r'(?:import|from)\s+\w+', code))
        
        elif language.lower() in ['javascript', 'js']:
            analysis['functions'] = len(re.findall(r'function\s+\w+|=>\s*{|\w+\s*:\s*function', code))
            analysis['classes'] = len(re.findall(r'class\s+\w+', code))
        
        # Determine complexity
        if analysis['line_count'] > 50:
            analysis['complexity'] = 'complex'
        elif analysis['line_count'] > 20:
            analysis['complexity'] = 'medium'
        
        return analysis
    
    def _is_code_query(self, query: str) -> bool:
        """
        Determine if a query is likely asking for code examples.
        
        Args:
            query: Search query
            
        Returns:
            bool: True if query appears to be code-related
        """
        code_keywords = [
            'function', 'method', 'class', 'example', 'code', 'implementation',
            'how to', 'tutorial', 'snippet', 'script', 'program', 'algorithm'
        ]
        
        query_lower = query.lower()
        return any(keyword in query_lower for keyword in code_keywords)
    
    async def _search_code_examples(
        self, 
        query: str, 
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search specifically for code examples.
        
        Args:
            query: Search query
            **kwargs: Additional parameters
            
        Returns:
            List[Dict]: Relevant code examples
        """
        # This would search the code examples table/index
        # For now, return empty list as placeholder
        return []
    
    async def _store_code_examples(
        self, 
        code_examples: List[Dict[str, Any]], 
        source_id: str
    ) -> None:
        """
        Store enriched code examples in database.
        
        Args:
            code_examples: Enriched code examples
            source_id: Source identifier
        """
        if not self.context.supabase_client:
            return
        
        try:
            # Would store in code_examples table
            # Implementation depends on database schema
            pass
        except Exception as e:
            self.logger.error(f"Failed to store code examples: {e}")
    
    def _merge_with_code_results(
        self, 
        regular_results: List[Dict[str, Any]], 
        code_results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Merge regular search results with code examples.
        
        Args:
            regular_results: Standard search results
            code_results: Code example results
            
        Returns:
            List[Dict]: Merged results
        """
        # Simple merge - in practice, you'd want more sophisticated ranking
        merged = regular_results.copy()
        
        # Add code results with appropriate metadata
        for code_result in code_results:
            code_result.setdefault('metadata', {}).update({
                'result_type': 'code_example',
                'enhanced_by': 'agentic_rag'
            })
            merged.append(code_result)
        
        return merged
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get strategy metadata."""
        metadata = super().get_metadata()
        metadata.update({
            "description": "Extracts and analyzes code examples with AI assistance",
            "type": "hybrid",
            "affects": ["content_processing", "code_search", "search_enhancement"],
            "capabilities": [
                "code_extraction",
                "code_summarization", 
                "code_analysis",
                "code_search"
            ]
        })
        return metadata