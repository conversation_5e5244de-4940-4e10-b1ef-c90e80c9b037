#!/usr/bin/env python3
"""
Crawl4AI MCP Server - Modular Implementation

A clean, modular MCP server for web crawling and RAG functionality.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.config.settings import Settings
from src.core.modular_server import create_server, run_server

# Configure logging
logging.basicConfig(
    level=os.environ.get("LOG_LEVEL", "INFO"),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main entry point for the MCP server."""
    # Load settings
    settings = Settings()
    
    # Validate configuration
    validation_result = settings.validate()
    if not validation_result.is_valid:
        logger.error("Configuration validation failed:")
        for error in validation_result.errors:
            logger.error(f"  - {error}")
        sys.exit(1)
        return  # Ensure we don't continue after exit
    
    if validation_result.warnings:
        logger.warning("Configuration warnings:")
        for warning in validation_result.warnings:
            logger.warning(f"  - {warning}")
    
    # Log health status
    health = settings.get_health_report()
    logger.info(f"Configuration health: {health['health']}")
    
    # Create and run server
    try:
        server = create_server(settings)
        logger.info(f"Starting Crawl4AI MCP server on {settings.server.host}:{settings.server.port}")
        
        # Get transport type from environment
        transport = os.environ.get("TRANSPORT", "sse")
        
        # Run server
        run_server(server, transport)
        
    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
    except Exception as e:
        logger.error(f"Server error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()