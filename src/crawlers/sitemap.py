"""
Sitemap crawler for XML sitemap parsing and URL extraction.

This crawler handles sitemap.xml files, corresponding to the 
original parse_sitemap function from the monolithic implementation.
"""

from typing import Dict, Any, List
import requests
import xml.etree.ElementTree as ElementTree
from .base import BaseCrawler, CrawlResult, CrawlType, URLDetector


class SitemapCrawler(BaseCrawler):
    """Crawler for XML sitemap parsing and URL extraction."""
    
    @property
    def crawler_type(self) -> CrawlType:
        """Return the crawler type."""
        return CrawlType.SITEMAP
    
    def can_handle(self, url: str) -> bool:
        """
        Check if this crawler can handle the given URL.
        
        Handles URLs that appear to be sitemaps (ending with sitemap.xml or containing 'sitemap').
        """
        return URLDetector.is_sitemap(url)
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for sitemap crawling."""
        return {
            'timeout': 30,
            'verify_ssl': True,
            'max_urls': 1000,  # Limit to prevent memory issues
            'include_subdirectories': True
        }
    
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """
        Parse a sitemap and extract URLs.
        
        Args:
            url: The URL to the sitemap.xml file
            **kwargs: Additional configuration options
                - timeout: Request timeout in seconds (default: 30)
                - verify_ssl: Whether to verify SSL certificates (default: True)
                - max_urls: Maximum number of URLs to extract (default: 1000)
                - include_subdirectories: Whether to include subdirectory URLs (default: True)
                
        Returns:
            CrawlResult: The result containing extracted URLs
        """
        if not await self.validate_url(url):
            return self._create_error_result(url, "Invalid URL format", "validation_error")
        
        if not self.can_handle(url):
            return self._create_error_result(url, "URL is not a sitemap", "unsupported_format")
        
        try:
            # Prepare configuration
            config = self.get_default_config()
            config.update(kwargs)
            
            # Fetch the sitemap
            urls = await self._parse_sitemap(url, config)
            
            if not urls:
                return self._create_error_result(url, "No URLs found in sitemap", "empty_content")
            
            # Apply URL limit if specified
            max_urls = config.get('max_urls', 1000)
            if len(urls) > max_urls:
                urls = urls[:max_urls]
            
            # Create metadata
            metadata = {
                'sitemap_url': url,
                'urls_count': len(urls),
                'max_urls_applied': len(urls) == max_urls,
                'include_subdirectories': config.get('include_subdirectories', True)
            }
            
            # Create content summary
            content = f"Sitemap parsed from {url}\nFound {len(urls)} URLs:\n\n" + \
                     "\n".join(f"- {u}" for u in urls[:10])  # Show first 10 URLs in content
            
            if len(urls) > 10:
                content += f"\n... and {len(urls) - 10} more URLs"
            
            return self._create_success_result(
                url=url,
                content=content,
                markdown=content,
                metadata=metadata,
                urls_found=urls
            )
            
        except Exception as e:
            return self._create_error_result(url, f"Error parsing sitemap: {str(e)}", "exception")
    
    async def _parse_sitemap(self, sitemap_url: str, config: Dict[str, Any]) -> List[str]:
        """
        Parse sitemap XML and extract URLs.
        
        Args:
            sitemap_url: URL to the sitemap
            config: Configuration options
            
        Returns:
            List[str]: Extracted URLs from the sitemap
        """
        urls = []
        
        try:
            # Make HTTP request to fetch sitemap
            timeout = config.get('timeout', 30)
            verify_ssl = config.get('verify_ssl', True)
            
            response = requests.get(sitemap_url, timeout=timeout, verify=verify_ssl)
            
            if response.status_code != 200:
                raise Exception(f"HTTP {response.status_code}: Failed to fetch sitemap")
            
            # Parse XML content
            try:
                tree = ElementTree.fromstring(response.content)
                
                # Extract URLs using namespace-agnostic approach
                # This handles both standard sitemaps and sitemaps with different namespaces
                url_elements = tree.findall('.//{*}loc')
                
                for loc in url_elements:
                    if loc.text:
                        url = loc.text.strip()
                        if url and self._should_include_url(url, config):
                            urls.append(url)
                
            except ElementTree.ParseError as e:
                raise Exception(f"Invalid XML format: {str(e)}")
                
        except requests.RequestException as e:
            raise Exception(f"Failed to fetch sitemap: {str(e)}")
        
        return urls
    
    def _should_include_url(self, url: str, config: Dict[str, Any]) -> bool:
        """
        Check if a URL should be included based on configuration.
        
        Args:
            url: The URL to check
            config: Configuration options
            
        Returns:
            bool: True if URL should be included
        """
        try:
            from urllib.parse import urlparse
            
            # Basic URL validation
            parsed = urlparse(url)
            if not all([parsed.scheme, parsed.netloc]):
                return False
            
            # Check if we should include subdirectories
            include_subdirectories = config.get('include_subdirectories', True)
            if not include_subdirectories:
                # Only include URLs from the root path
                if parsed.path and parsed.path != '/' and '/' in parsed.path.strip('/'):
                    return False
            
            return True
            
        except Exception:
            return False