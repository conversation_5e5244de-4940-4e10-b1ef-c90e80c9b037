"""
Smart crawler for intelligent URL routing and type-based processing.

This crawler implements the smart routing logic corresponding to the 
original smart_crawl_url function from the monolithic implementation.
"""

from typing import Dict, Any, List, Optional
from .base import BaseCrawler, CrawlResult, CrawlType, URLDetector
from .single_page import SinglePageCrawler
from .text_file import TextFileCrawler
from .sitemap import SitemapCrawler
from .batch import BatchCrawler
from .recursive import RecursiveCrawler


class SmartCrawler(BaseCrawler):
    """
    Smart crawler that automatically detects URL types and routes to appropriate crawlers.
    
    This crawler implements the intelligent routing logic that determines the best
    crawling strategy based on URL patterns and content types.
    """
    
    def __init__(self, crawler_instance: Any = None, **kwargs):
        """Initialize the smart crawler with specialized crawler instances."""
        super().__init__(crawler_instance, **kwargs)
        
        # Initialize specialized crawlers
        self.single_page_crawler = SinglePageCrawler(crawler_instance, **kwargs)
        self.text_file_crawler = TextFileCrawler(crawler_instance, **kwargs)
        self.sitemap_crawler = SitemapCrawler(crawler_instance, **kwargs)
        self.batch_crawler = BatchCrawler(crawler_instance, **kwargs)
        self.recursive_crawler = RecursiveCrawler(crawler_instance, **kwargs)
        
        # Crawler registry for routing
        self.crawler_registry = {
            CrawlType.SINGLE_PAGE: self.single_page_crawler,
            CrawlType.TEXT_FILE: self.text_file_crawler,
            CrawlType.SITEMAP: self.sitemap_crawler,
            CrawlType.BATCH: self.batch_crawler,
            CrawlType.RECURSIVE: self.recursive_crawler
        }
    
    @property
    def crawler_type(self) -> CrawlType:
        """Return the crawler type."""
        return CrawlType.SINGLE_PAGE  # Smart crawler acts as single page by default
    
    def can_handle(self, url: str) -> bool:
        """
        Check if this crawler can handle the given URL.
        
        Smart crawler can handle any URL by routing to appropriate specialized crawlers.
        """
        return True
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for smart crawling."""
        return {
            'max_depth': 3,
            'max_concurrent': 10,
            'auto_detect_batch': True,
            'follow_internal_links': False,
            'sitemap_auto_batch': True
        }
    
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """
        Intelligently crawl a URL by detecting its type and using the appropriate crawler.
        
        Args:
            url: The URL to crawl
            **kwargs: Additional configuration options
                - max_depth: Maximum depth for recursive crawling (default: 3)
                - max_concurrent: Maximum concurrent crawls (default: 10)
                - auto_detect_batch: Automatically use batch processing for sitemaps (default: True)
                - follow_internal_links: Whether to follow internal links recursively (default: False)
                - sitemap_auto_batch: Automatically batch process sitemap URLs (default: True)
                
        Returns:
            CrawlResult: The result of the crawling operation
        """
        if not await self.validate_url(url):
            return self._create_error_result(url, "Invalid URL format", "validation_error")
        
        try:
            # Prepare configuration
            config = self.get_default_config()
            config.update(kwargs)
            
            # Detect URL type
            url_type = URLDetector.detect_url_type(url)
            
            # Route to appropriate crawler based on URL type and configuration
            return await self._route_crawl(url, url_type, config)
            
        except Exception as e:
            return self._create_error_result(url, f"Smart crawling failed: {str(e)}", "exception")
    
    async def _route_crawl(self, url: str, url_type: CrawlType, config: Dict[str, Any]) -> CrawlResult:
        """
        Route crawling to the appropriate specialized crawler.
        
        Args:
            url: The URL to crawl
            url_type: Detected URL type
            config: Configuration options
            
        Returns:
            CrawlResult: The result of the specialized crawling operation
        """
        try:
            if url_type == CrawlType.TEXT_FILE:
                # Handle text/markdown files
                return await self.text_file_crawler.crawl(url, **config)
                
            elif url_type == CrawlType.SITEMAP:
                # Handle sitemaps with optional auto-batch processing
                sitemap_result = await self.sitemap_crawler.crawl(url, **config)
                
                if sitemap_result.success and config.get('sitemap_auto_batch', True):
                    # If sitemap crawl successful and auto-batch enabled, crawl all URLs
                    if sitemap_result.urls_found:
                        batch_results = await self.batch_crawler.crawl_batch(
                            sitemap_result.urls_found, 
                            max_concurrent=config.get('max_concurrent', 10)
                        )
                        
                        # Combine sitemap result with batch results
                        return self._combine_sitemap_with_batch(sitemap_result, batch_results)
                
                return sitemap_result
                
            else:
                # Handle regular webpages (CrawlType.SINGLE_PAGE)
                if config.get('follow_internal_links', False):
                    # Use recursive crawler for internal link following
                    recursive_results = await self.recursive_crawler.crawl_recursive(
                        [url],
                        max_depth=config.get('max_depth', 3),
                        max_concurrent=config.get('max_concurrent', 10)
                    )
                    
                    if recursive_results:
                        return recursive_results[0]  # Return result for the main URL
                    else:
                        return self._create_error_result(url, "Recursive crawling returned no results", "empty_result")
                else:
                    # Use single page crawler
                    return await self.single_page_crawler.crawl(url, **config)
            
        except Exception as e:
            return self._create_error_result(url, f"Routing failed: {str(e)}", "routing_error")
    
    def _combine_sitemap_with_batch(self, sitemap_result: CrawlResult, 
                                   batch_results: List[CrawlResult]) -> CrawlResult:
        """
        Combine sitemap parsing result with batch crawling results.
        
        Args:
            sitemap_result: Result from sitemap parsing
            batch_results: Results from batch crawling sitemap URLs
            
        Returns:
            CrawlResult: Combined result
        """
        try:
            successful_batch = [r for r in batch_results if r.success]
            failed_batch = [r for r in batch_results if not r.success]
            
            # Create combined content
            content_parts = [sitemap_result.content or ""]
            
            if successful_batch:
                content_parts.append(f"\n\nBatch crawl results ({len(successful_batch)} successful):")
                for i, result in enumerate(successful_batch[:5]):  # Show first 5 results
                    content_parts.append(f"\n{i+1}. {result.url}: {len(result.content or '')} chars")
                
                if len(successful_batch) > 5:
                    content_parts.append(f"\n... and {len(successful_batch) - 5} more successful results")
            
            if failed_batch:
                content_parts.append(f"\n\nFailed URLs ({len(failed_batch)}):")
                for result in failed_batch[:3]:  # Show first 3 failures
                    error_msg = result.error.error_message if result.error else "Unknown error"
                    content_parts.append(f"\n- {result.url}: {error_msg}")
                
                if len(failed_batch) > 3:
                    content_parts.append(f"\n... and {len(failed_batch) - 3} more failed URLs")
            
            combined_content = "".join(content_parts)
            
            # Create combined metadata
            combined_metadata = sitemap_result.metadata.copy() if sitemap_result.metadata else {}
            combined_metadata.update({
                'batch_crawl_enabled': True,
                'batch_total': len(batch_results),
                'batch_successful': len(successful_batch),
                'batch_failed': len(failed_batch),
                'batch_success_rate': round((len(successful_batch) / len(batch_results) * 100) if batch_results else 0, 2)
            })
            
            # Collect all found URLs from successful batch results
            all_urls = sitemap_result.urls_found or []
            for result in successful_batch:
                if result.urls_found:
                    all_urls.extend(result.urls_found)
            
            return self._create_success_result(
                url=sitemap_result.url,
                content=combined_content,
                markdown=combined_content,
                metadata=combined_metadata,
                urls_found=all_urls
            )
            
        except Exception as e:
            # If combination fails, return original sitemap result
            return sitemap_result
    
    async def crawl_multiple(self, urls: List[str], **kwargs) -> List[CrawlResult]:
        """
        Crawl multiple URLs intelligently with smart routing.
        
        Args:
            urls: List of URLs to crawl
            **kwargs: Configuration options for smart crawling
            
        Returns:
            List[CrawlResult]: Results for each URL
        """
        if not urls:
            return []
        
        results = []
        for url in urls:
            result = await self.crawl(url, **kwargs)
            results.append(result)
        
        return results
    
    def get_routing_info(self, url: str) -> Dict[str, Any]:
        """
        Get information about how a URL would be routed.
        
        Args:
            url: The URL to analyze
            
        Returns:
            Dict[str, Any]: Routing information
        """
        url_type = URLDetector.detect_url_type(url)
        crawler = self.crawler_registry.get(url_type, self.single_page_crawler)
        
        return {
            'url': url,
            'detected_type': url_type.value,
            'crawler_class': crawler.__class__.__name__,
            'can_handle': crawler.can_handle(url),
            'is_sitemap': URLDetector.is_sitemap(url),
            'is_text_file': URLDetector.is_text_file(url),
            'is_robots_txt': URLDetector.is_robots_txt(url)
        }