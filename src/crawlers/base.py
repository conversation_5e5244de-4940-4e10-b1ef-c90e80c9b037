"""
Base crawler interface and common types for the crawler module.

This module defines the abstract base class for all crawlers and common
data structures used across the crawling system.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import json


class CrawlType(Enum):
    """Enumeration of supported crawl types."""
    SINGLE_PAGE = "single_page"
    TEXT_FILE = "text_file" 
    SITEMAP = "sitemap"
    BATCH = "batch"
    RECURSIVE = "recursive"
    ROBOTS_TXT = "robots_txt"


@dataclass
class CrawlError:
    """Represents an error that occurred during crawling."""
    url: str
    error_message: str
    error_type: str = "crawl_error"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary format."""
        return {
            "success": False,
            "url": self.url,
            "error": self.error_message,
            "error_type": self.error_type
        }
    
    def to_json(self, indent: int = 2) -> str:
        """Convert error to JSON format."""
        return json.dumps(self.to_dict(), indent=indent)


@dataclass 
class CrawlResult:
    """Represents the result of a crawling operation."""
    success: bool
    url: str
    content: Optional[str] = None
    markdown: Optional[str] = None
    crawl_type: Optional[CrawlType] = None
    metadata: Optional[Dict[str, Any]] = None
    urls_found: Optional[List[str]] = None
    error: Optional[CrawlError] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary format."""
        result = {
            "success": self.success,
            "url": self.url
        }
        
        if self.success:
            if self.content:
                result["content"] = self.content
            if self.markdown:
                result["markdown"] = self.markdown
            if self.crawl_type:
                result["crawl_type"] = self.crawl_type.value
            if self.metadata:
                result["metadata"] = self.metadata
            if self.urls_found:
                result["urls_found"] = self.urls_found
        else:
            if self.error:
                result.update(self.error.to_dict())
                
        return result
    
    def to_json(self, indent: int = 2) -> str:
        """Convert result to JSON format."""
        return json.dumps(self.to_dict(), indent=indent)

    @classmethod
    def from_error(cls, url: str, error_message: str, error_type: str = "crawl_error") -> "CrawlResult":
        """Create a failed CrawlResult from an error."""
        error = CrawlError(url=url, error_message=error_message, error_type=error_type)
        return cls(success=False, url=url, error=error)

    @classmethod
    def from_success(cls, url: str, content: str = None, markdown: str = None, 
                    crawl_type: CrawlType = None, metadata: Dict[str, Any] = None,
                    urls_found: List[str] = None) -> "CrawlResult":
        """Create a successful CrawlResult."""
        return cls(
            success=True,
            url=url,
            content=content,
            markdown=markdown,
            crawl_type=crawl_type,
            metadata=metadata,
            urls_found=urls_found
        )


class BaseCrawler(ABC):
    """
    Abstract base class for all crawlers.
    
    This class defines the interface that all crawler implementations must follow.
    Each crawler type specializes in handling specific URL patterns or crawling strategies.
    """
    
    def __init__(self, crawler_instance: Any = None, **kwargs):
        """
        Initialize the crawler.
        
        Args:
            crawler_instance: The actual crawl4ai crawler instance
            **kwargs: Additional configuration options
        """
        self.crawler_instance = crawler_instance
        self.config = kwargs
    
    @abstractmethod
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """
        Crawl a URL and return the result.
        
        Args:
            url: The URL to crawl
            **kwargs: Additional parameters specific to the crawler type
            
        Returns:
            CrawlResult: The result of the crawling operation
        """
        pass
    
    @abstractmethod
    def can_handle(self, url: str) -> bool:
        """
        Check if this crawler can handle the given URL.
        
        Args:
            url: The URL to check
            
        Returns:
            bool: True if this crawler can handle the URL, False otherwise
        """
        pass
    
    @property
    @abstractmethod
    def crawler_type(self) -> CrawlType:
        """Return the type of crawler this is."""
        pass
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for this crawler type."""
        return {}
    
    async def validate_url(self, url: str) -> bool:
        """
        Validate that the URL is accessible and well-formed.
        
        Args:
            url: The URL to validate
            
        Returns:
            bool: True if the URL is valid, False otherwise
        """
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return all([parsed.scheme, parsed.netloc])
        except Exception:
            return False
    
    def _create_error_result(self, url: str, error_message: str, error_type: str = "crawl_error") -> CrawlResult:
        """Helper method to create error results."""
        return CrawlResult.from_error(url, error_message, error_type)
    
    def _create_success_result(self, url: str, **kwargs) -> CrawlResult:
        """Helper method to create success results."""
        kwargs['crawl_type'] = self.crawler_type
        return CrawlResult.from_success(url, **kwargs)


class URLDetector:
    """Utility class for detecting URL types and routing to appropriate crawlers."""
    
    @staticmethod
    def is_sitemap(url: str) -> bool:
        """Check if URL appears to be a sitemap."""
        from urllib.parse import urlparse
        return url.endswith('sitemap.xml') or 'sitemap' in urlparse(url).path.lower()
    
    @staticmethod  
    def is_text_file(url: str) -> bool:
        """Check if URL appears to be a text or markdown file."""
        return url.endswith(('.txt', '.md', '.markdown'))
    
    @staticmethod
    def is_robots_txt(url: str) -> bool:
        """Check if URL is a robots.txt file."""
        return url.endswith('/robots.txt') or url.endswith('robots.txt')
    
    @staticmethod
    def detect_url_type(url: str) -> CrawlType:
        """
        Detect the appropriate crawl type for a URL.
        
        Args:
            url: The URL to analyze
            
        Returns:
            CrawlType: The detected crawl type
        """
        if URLDetector.is_robots_txt(url):
            return CrawlType.ROBOTS_TXT
        elif URLDetector.is_sitemap(url):
            return CrawlType.SITEMAP
        elif URLDetector.is_text_file(url):
            return CrawlType.TEXT_FILE
        else:
            return CrawlType.SINGLE_PAGE