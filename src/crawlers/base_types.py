"""Base types and interfaces for the modular crawler system."""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Any


class URLType(Enum):
    """Types of URLs that can be crawled."""
    WEBPAGE = "webpage"
    SITEMAP = "sitemap"
    ROBOTS_TXT = "robots_txt"
    TEXT_FILE = "text_file"
    UNKNOWN = "unknown"


@dataclass
class CrawlError:
    """Represents an error during crawling."""
    message: str
    error_type: str
    details: Optional[Dict[str, Any]] = None
    
    def __str__(self) -> str:
        """String representation of the error."""
        return f"[{self.error_type}] {self.message}"


@dataclass
class CrawlResult:
    """Result of a crawl operation."""
    url: str
    content: str
    title: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    links: List[str] = field(default_factory=list)
    media: Dict[str, Any] = field(default_factory=dict)
    error: Optional[CrawlError] = None


class BaseCrawler(ABC):
    """Abstract base class for all crawlers."""
    
    @abstractmethod
    def can_handle(self, url: str) -> bool:
        """Check if this crawler can handle the given URL."""
        pass
    
    @abstractmethod
    async def crawl(self, url: str, options: Optional[Dict[str, Any]] = None) -> CrawlResult:
        """Crawl the given URL and return the result."""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the name of this crawler."""
        pass


class URLDetector:
    """Detects the type of a URL."""
    
    def detect_type(self, url: str) -> URLType:
        """Detect the type of the given URL."""
        url_lower = url.lower()
        
        # Remove query parameters and fragments
        if '?' in url_lower:
            url_lower = url_lower.split('?')[0]
        if '#' in url_lower:
            url_lower = url_lower.split('#')[0]
        
        # Check for specific file types
        if url_lower.endswith('/robots.txt'):
            return URLType.ROBOTS_TXT
        elif url_lower.endswith('.xml') and 'sitemap' in url_lower:
            return URLType.SITEMAP
        elif any(url_lower.endswith(ext) for ext in ['.txt', '.md', '.text', '.markdown']):
            return URLType.TEXT_FILE
        elif any(url_lower.endswith(ext) for ext in ['.html', '.htm', '.php', '.asp', '.aspx', '.jsp']):
            return URLType.WEBPAGE
        elif any(ext in url_lower for ext in ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.mp4', '.avi']):
            return URLType.UNKNOWN
        else:
            # Default to webpage for URLs without extensions
            return URLType.WEBPAGE