"""
Single page crawler for basic webpage crawling.

This crawler handles simple single-page crawling operations, corresponding to the
original crawl_single_page function from the monolithic implementation.
"""

from typing import Dict, Any
from crawl4ai import CrawlerRunConfig, CacheMode
from .base import BaseCrawler, CrawlResult, CrawlType


class SinglePageCrawler(BaseCrawler):
    """Crawler for single webpage crawling operations."""
    
    @property
    def crawler_type(self) -> CrawlType:
        """Return the crawler type."""
        return CrawlType.SINGLE_PAGE
    
    def can_handle(self, url: str) -> bool:
        """
        Check if this crawler can handle the given URL.
        
        Single page crawler is the default fallback, so it can handle any HTTP(S) URL
        that isn't specialized by other crawlers.
        """
        from urllib.parse import urlparse
        try:
            parsed = urlparse(url)
            return parsed.scheme in ('http', 'https')
        except Exception:
            return False
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for single page crawling."""
        return {
            'cache_mode': CacheMode.BYPASS,
            'stream': False,
            'bypass_cache': True
        }
    
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """
        Crawl a single webpage.
        
        Args:
            url: The URL to crawl
            **kwargs: Additional configuration options
                - bypass_cache: Whether to bypass cache (default: True)
                - stream: Whether to use streaming (default: False)
                - Any other CrawlerRunConfig parameters
                
        Returns:
            CrawlResult: The result of the crawling operation
        """
        if not await self.validate_url(url):
            return self._create_error_result(url, "Invalid URL format", "validation_error")
        
        if not self.crawler_instance:
            return self._create_error_result(url, "Crawler instance not provided", "configuration_error")
        
        try:
            # Prepare crawler configuration
            config_options = self.get_default_config()
            config_options.update(kwargs)
            
            # Extract CrawlerRunConfig specific options
            crawler_config = CrawlerRunConfig(
                cache_mode=config_options.get('cache_mode', CacheMode.BYPASS),
                stream=config_options.get('stream', False)
            )
            
            # Perform the crawl
            result = await self.crawler_instance.arun(
                url=url,
                config=crawler_config
            )
            
            # Check if crawl was successful
            if not result.success:
                error_msg = result.error_message if hasattr(result, 'error_message') else "Crawl failed"
                return self._create_error_result(url, error_msg, "crawl_failure")
            
            if not result.markdown:
                return self._create_error_result(url, "No content found", "empty_content")
            
            # Create successful result
            metadata = {
                'title': getattr(result, 'metadata', {}).get('title', '') if hasattr(result, 'metadata') else '',
                'url': url,
                'content_length': len(result.markdown) if result.markdown else 0,
                'crawl_timestamp': getattr(result, 'timestamp', None) if hasattr(result, 'timestamp') else None
            }
            
            return self._create_success_result(
                url=url,
                content=result.markdown,
                markdown=result.markdown,
                metadata=metadata
            )
            
        except Exception as e:
            return self._create_error_result(url, str(e), "exception")