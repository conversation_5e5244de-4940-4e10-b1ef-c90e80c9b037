"""
Batch crawler for parallel processing of multiple URLs.

This crawler handles concurrent crawling of multiple URLs, corresponding to the 
original crawl_batch function from the monolithic implementation.
"""

from typing import Dict, Any, List
import asyncio
from crawl4ai import CrawlerRunConfig, CacheMode, MemoryAdaptiveDispatcher
from .base import BaseCrawler, CrawlResult, CrawlType


class BatchCrawler(BaseCrawler):
    """Crawler for parallel batch processing of multiple URLs."""
    
    @property
    def crawler_type(self) -> CrawlType:
        """Return the crawler type."""
        return CrawlType.BATCH
    
    def can_handle(self, url: str) -> bool:
        """
        Check if this crawler can handle the given URL.
        
        Batch crawler doesn't handle individual URLs, but rather lists of URLs.
        This method is mainly for interface compliance.
        """
        return True  # Can handle any valid URLs for batch processing
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for batch crawling."""
        return {
            'max_concurrent': 10,
            'memory_threshold_percent': 70.0,
            'check_interval': 1.0,
            'cache_mode': CacheMode.BYPASS,
            'stream': False,
            'timeout_per_url': 30
        }
    
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """
        This method is not used for batch crawling.
        Use crawl_batch() instead for batch operations.
        """
        return self._create_error_result(url, "Use crawl_batch() method for batch operations", "method_error")
    
    async def crawl_batch(self, urls: List[str], **kwargs) -> List[CrawlResult]:
        """
        Crawl multiple URLs in parallel.
        
        Args:
            urls: List of URLs to crawl
            **kwargs: Additional configuration options
                - max_concurrent: Maximum concurrent crawls (default: 10)
                - memory_threshold_percent: Memory threshold for adaptive dispatcher (default: 70.0)
                - check_interval: Check interval for memory monitoring (default: 1.0)
                - cache_mode: Cache mode for crawling (default: BYPASS)
                - stream: Whether to use streaming (default: False)
                - timeout_per_url: Timeout per URL in seconds (default: 30)
                
        Returns:
            List[CrawlResult]: Results for each URL crawled
        """
        if not urls:
            return []
        
        if not self.crawler_instance:
            error_result = self._create_error_result("batch", "Crawler instance not provided", "configuration_error")
            return [error_result] * len(urls)
        
        try:
            # Prepare configuration
            config = self.get_default_config()
            config.update(kwargs)
            
            # Setup memory adaptive dispatcher
            dispatcher = MemoryAdaptiveDispatcher(
                memory_threshold_percent=config.get('memory_threshold_percent', 70.0),
                check_interval=config.get('check_interval', 1.0),
                max_session_permit=config.get('max_concurrent', 10)
            )
            
            # Prepare crawler configuration
            crawler_config = CrawlerRunConfig(
                cache_mode=config.get('cache_mode', CacheMode.BYPASS),
                stream=config.get('stream', False)
            )
            
            # Create crawl tasks
            crawl_configs = [crawler_config] * len(urls)
            
            # Execute batch crawling
            results = await self.crawler_instance.arun_many(
                urls=urls,
                configs=crawl_configs,
                dispatcher=dispatcher
            )
            
            # Convert crawl4ai results to CrawlResult objects
            crawl_results = []
            for i, (url, result) in enumerate(zip(urls, results)):
                crawl_result = await self._convert_result(url, result, i)
                crawl_results.append(crawl_result)
            
            return crawl_results
            
        except Exception as e:
            # If batch processing fails entirely, return error for all URLs
            error_result = self._create_error_result("batch", f"Batch processing failed: {str(e)}", "exception")
            return [error_result] * len(urls)
    
    async def _convert_result(self, url: str, result: Any, index: int) -> CrawlResult:
        """
        Convert a crawl4ai result to a CrawlResult object.
        
        Args:
            url: The URL that was crawled
            result: The crawl4ai result object
            index: Index of this result in the batch
            
        Returns:
            CrawlResult: Converted result
        """
        try:
            # Check if crawl was successful
            if not result or not hasattr(result, 'success') or not result.success:
                error_msg = result.error_message if hasattr(result, 'error_message') else "Crawl failed"
                return self._create_error_result(url, error_msg, "crawl_failure")
            
            if not result.markdown:
                return self._create_error_result(url, "No content found", "empty_content")
            
            # Create metadata
            metadata = {
                'batch_index': index,
                'title': getattr(result, 'metadata', {}).get('title', '') if hasattr(result, 'metadata') else '',
                'url': url,
                'content_length': len(result.markdown) if result.markdown else 0,
                'crawl_timestamp': getattr(result, 'timestamp', None) if hasattr(result, 'timestamp') else None
            }
            
            return self._create_success_result(
                url=url,
                content=result.markdown,
                markdown=result.markdown,
                metadata=metadata
            )
            
        except Exception as e:
            return self._create_error_result(url, f"Error processing result: {str(e)}", "processing_error")
    
    def get_successful_results(self, results: List[CrawlResult]) -> List[CrawlResult]:
        """
        Filter batch results to only include successful crawls.
        
        Args:
            results: List of CrawlResult objects from batch operation
            
        Returns:
            List[CrawlResult]: Only successful results
        """
        return [result for result in results if result.success]
    
    def get_failed_results(self, results: List[CrawlResult]) -> List[CrawlResult]:
        """
        Filter batch results to only include failed crawls.
        
        Args:
            results: List of CrawlResult objects from batch operation
            
        Returns:
            List[CrawlResult]: Only failed results
        """
        return [result for result in results if not result.success]
    
    def get_batch_summary(self, results: List[CrawlResult]) -> Dict[str, Any]:
        """
        Get summary statistics for a batch crawl operation.
        
        Args:
            results: List of CrawlResult objects from batch operation
            
        Returns:
            Dict[str, Any]: Summary statistics
        """
        total = len(results)
        successful = len(self.get_successful_results(results))
        failed = len(self.get_failed_results(results))
        
        success_rate = (successful / total * 100) if total > 0 else 0
        
        return {
            'total_urls': total,
            'successful': successful,
            'failed': failed,
            'success_rate': round(success_rate, 2),
            'failed_urls': [result.url for result in results if not result.success]
        }