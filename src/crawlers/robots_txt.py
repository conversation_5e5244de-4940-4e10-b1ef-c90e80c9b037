"""
Robots.txt crawler for handling robots.txt files and crawling permissions.

This crawler adds missing robots.txt functionality to parse and respect
robots.txt directives for web crawling.
"""

from typing import Dict, Any, List, Optional
import requests
from urllib.parse import urljoin, urlparse
from .base import BaseCrawler, CrawlResult, CrawlType, URLDetector


class RobotsTxtCrawler(BaseCrawler):
    """Crawler for robots.txt file parsing and crawling permission checks."""
    
    @property
    def crawler_type(self) -> CrawlType:
        """Return the crawler type."""
        return CrawlType.ROBOTS_TXT
    
    def can_handle(self, url: str) -> bool:
        """
        Check if this crawler can handle the given URL.
        
        Handles robots.txt files specifically.
        """
        return URLDetector.is_robots_txt(url)
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for robots.txt crawling."""
        return {
            'timeout': 10,
            'verify_ssl': True,
            'user_agent': '*',
            'parse_directives': True
        }
    
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """
        Crawl and parse a robots.txt file.
        
        Args:
            url: The URL to the robots.txt file
            **kwargs: Additional configuration options
                - timeout: Request timeout in seconds (default: 10)
                - verify_ssl: Whether to verify SSL certificates (default: True)
                - user_agent: User agent to check permissions for (default: '*')
                - parse_directives: Whether to parse robots.txt directives (default: True)
                
        Returns:
            CrawlResult: The result containing parsed robots.txt information
        """
        if not await self.validate_url(url):
            return self._create_error_result(url, "Invalid URL format", "validation_error")
        
        if not self.can_handle(url):
            return self._create_error_result(url, "URL is not a robots.txt file", "unsupported_format")
        
        try:
            # Prepare configuration
            config = self.get_default_config()
            config.update(kwargs)
            
            # Fetch robots.txt content
            robots_content = await self._fetch_robots_txt(url, config)
            
            if not robots_content:
                return self._create_error_result(url, "No robots.txt content found", "empty_content")
            
            # Parse robots.txt if requested
            parsed_rules = None
            if config.get('parse_directives', True):
                parsed_rules = self._parse_robots_txt(robots_content, config.get('user_agent', '*'))
            
            # Create metadata
            metadata = {
                'robots_txt_url': url,
                'content_length': len(robots_content),
                'user_agent': config.get('user_agent', '*'),
                'parsed_rules': parsed_rules,
                'rules_count': len(parsed_rules) if parsed_rules else 0
            }
            
            # Create content summary
            content = f"Robots.txt content from {url}\n\n"
            content += f"Raw content ({len(robots_content)} characters):\n"
            content += "=" * 50 + "\n"
            content += robots_content
            
            if parsed_rules:
                content += "\n\n" + "=" * 50 + "\n"
                content += f"Parsed rules for user-agent '{config.get('user_agent', '*')}':\n"
                content += f"- Allowed paths: {len(parsed_rules.get('allow', []))}\n"
                content += f"- Disallowed paths: {len(parsed_rules.get('disallow', []))}\n"
                content += f"- Crawl delay: {parsed_rules.get('crawl_delay', 'Not specified')}\n"
                content += f"- Sitemap URLs: {len(parsed_rules.get('sitemap', []))}\n"
            
            return self._create_success_result(
                url=url,
                content=content,
                markdown=content,
                metadata=metadata
            )
            
        except Exception as e:
            return self._create_error_result(url, f"Error crawling robots.txt: {str(e)}", "exception")
    
    async def _fetch_robots_txt(self, url: str, config: Dict[str, Any]) -> Optional[str]:
        """
        Fetch robots.txt content via HTTP request.
        
        Args:
            url: URL to the robots.txt file
            config: Configuration options
            
        Returns:
            Optional[str]: The robots.txt content or None if failed
        """
        try:
            timeout = config.get('timeout', 10)
            verify_ssl = config.get('verify_ssl', True)
            
            response = requests.get(url, timeout=timeout, verify=verify_ssl)
            
            if response.status_code == 200:
                return response.text
            elif response.status_code == 404:
                # 404 for robots.txt is common and not necessarily an error
                return f"# No robots.txt found (HTTP 404)\n# URL: {url}\n"
            else:
                raise Exception(f"HTTP {response.status_code}: Failed to fetch robots.txt")
                
        except requests.RequestException as e:
            raise Exception(f"Failed to fetch robots.txt: {str(e)}")
    
    def _parse_robots_txt(self, content: str, user_agent: str = '*') -> Dict[str, Any]:
        """
        Parse robots.txt content and extract rules for specified user agent.
        
        Args:
            content: Raw robots.txt content
            user_agent: User agent to parse rules for
            
        Returns:
            Dict[str, Any]: Parsed rules including allow, disallow, crawl-delay, sitemap
        """
        rules = {
            'allow': [],
            'disallow': [],
            'crawl_delay': None,
            'sitemap': []
        }
        
        try:
            lines = content.split('\n')
            current_user_agent = None
            in_matching_section = False
            
            for line in lines:
                line = line.strip()
                
                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue
                
                # Parse directive
                if ':' in line:
                    directive, value = line.split(':', 1)
                    directive = directive.strip().lower()
                    value = value.strip()
                    
                    if directive == 'user-agent':
                        current_user_agent = value
                        # Check if this section applies to our user agent
                        in_matching_section = (value == user_agent or 
                                             value == '*' or 
                                             user_agent == '*')
                    
                    elif in_matching_section:
                        if directive == 'allow':
                            rules['allow'].append(value)
                        elif directive == 'disallow':
                            rules['disallow'].append(value)
                        elif directive == 'crawl-delay':
                            try:
                                rules['crawl_delay'] = float(value)
                            except ValueError:
                                pass
                    
                    # Sitemap is global, not user-agent specific
                    if directive == 'sitemap':
                        rules['sitemap'].append(value)
            
        except Exception:
            pass  # Ignore parsing errors, return partial results
        
        return rules
    
    def check_crawl_permission(self, robots_rules: Dict[str, Any], path: str) -> bool:
        """
        Check if crawling a specific path is allowed based on robots.txt rules.
        
        Args:
            robots_rules: Parsed robots.txt rules from _parse_robots_txt
            path: URL path to check (e.g., '/about', '/api/users')
            
        Returns:
            bool: True if crawling is allowed, False if disallowed
        """
        try:
            # Check disallow rules first (more restrictive)
            for disallow_pattern in robots_rules.get('disallow', []):
                if disallow_pattern == '':
                    continue  # Empty disallow means nothing is disallowed
                if path.startswith(disallow_pattern):
                    # Check if there's a more specific allow rule
                    for allow_pattern in robots_rules.get('allow', []):
                        if allow_pattern and path.startswith(allow_pattern):
                            if len(allow_pattern) > len(disallow_pattern):
                                return True  # More specific allow overrides disallow
                    return False  # Disallowed
            
            # If not explicitly disallowed, check allow rules
            allow_rules = robots_rules.get('allow', [])
            if allow_rules:
                # If there are allow rules, path must match one of them
                for allow_pattern in allow_rules:
                    if allow_pattern and path.startswith(allow_pattern):
                        return True
                return False  # Not in allow list
            
            # No specific rules, default to allowed
            return True
            
        except Exception:
            return True  # On error, default to allowing crawl
    
    def get_robots_txt_url(self, base_url: str) -> str:
        """
        Generate the robots.txt URL for a given base URL.
        
        Args:
            base_url: The base URL of the website
            
        Returns:
            str: The robots.txt URL
        """
        try:
            parsed = urlparse(base_url)
            robots_url = f"{parsed.scheme}://{parsed.netloc}/robots.txt"
            return robots_url
        except Exception:
            return urljoin(base_url, '/robots.txt')