"""
Crawler module for modular web crawling functionality.

This module provides specialized crawlers for different URL types and crawling strategies,
following the strategy pattern for clean separation of concerns.
"""

from .base import BaseCrawler, <PERSON><PERSON>l<PERSON><PERSON>ult, CrawlError, CrawlType, URLDetector
from .manager import CrawlerManager
from .single_page import SinglePageCrawler
from .text_file import TextFileCrawler
from .sitemap import SitemapCrawler
from .batch import BatchCrawler
from .recursive import RecursiveCrawler
from .smart import SmartCrawler
from .robots_txt import RobotsTxtCrawler

__all__ = [
    # Base classes and types
    'BaseCrawler',
    'CrawlResult', 
    'CrawlError',
    'CrawlType',
    'URLDetector',
    
    # Manager
    'CrawlerManager',
    
    # Specialized crawlers
    'SinglePageCrawler',
    'TextFileCrawler',
    'SitemapCrawler',
    'BatchCrawler',
    'RecursiveCrawler',
    'SmartCrawler',
    'RobotsTxtCrawler'
]