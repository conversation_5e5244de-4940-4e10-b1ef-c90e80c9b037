"""
Recursive crawler for following internal links with depth control.

This crawler handles recursive link following, corresponding to the 
original crawl_recursive_internal_links function from the monolithic implementation.
"""

from typing import Dict, Any, List, Set
from urllib.parse import urljoin, urlparse, urlunparse
from crawl4ai import CrawlerRunConfig, CacheMode, MemoryAdaptiveDispatcher
from .base import BaseCrawler, CrawlResult, CrawlType


class RecursiveCrawler(BaseCrawler):
    """Crawler for recursive internal link following with depth control."""
    
    @property
    def crawler_type(self) -> CrawlType:
        """Return the crawler type."""
        return CrawlType.RECURSIVE
    
    def can_handle(self, url: str) -> bool:
        """
        Check if this crawler can handle the given URL.
        
        Recursive crawler can handle any HTTP(S) URL for link following.
        """
        from urllib.parse import urlparse
        try:
            parsed = urlparse(url)
            return parsed.scheme in ('http', 'https')
        except Exception:
            return False
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for recursive crawling."""
        return {
            'max_depth': 3,
            'max_concurrent': 10,
            'memory_threshold_percent': 70.0,
            'check_interval': 1.0,
            'cache_mode': CacheMode.BYPASS,
            'stream': False,
            'same_domain_only': True,
            'max_urls_per_domain': 100
        }
    
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """
        This method is not used for recursive crawling.
        Use crawl_recursive() instead for recursive operations.
        """
        return self._create_error_result(url, "Use crawl_recursive() method for recursive operations", "method_error")
    
    async def crawl_recursive(self, start_urls: List[str], **kwargs) -> List[CrawlResult]:
        """
        Crawl URLs recursively following internal links.
        
        Args:
            start_urls: List of starting URLs
            **kwargs: Additional configuration options
                - max_depth: Maximum recursion depth (default: 3)
                - max_concurrent: Maximum concurrent crawls (default: 10)
                - memory_threshold_percent: Memory threshold (default: 70.0)
                - check_interval: Memory check interval (default: 1.0)
                - cache_mode: Cache mode for crawling (default: BYPASS)
                - stream: Whether to use streaming (default: False)
                - same_domain_only: Only follow links within same domain (default: True)
                - max_urls_per_domain: Maximum URLs per domain (default: 100)
                
        Returns:
            List[CrawlResult]: Results for all crawled URLs
        """
        if not start_urls:
            return []
        
        if not self.crawler_instance:
            error_result = self._create_error_result("recursive", "Crawler instance not provided", "configuration_error")
            return [error_result]
        
        try:
            # Prepare configuration
            config = self.get_default_config()
            config.update(kwargs)
            
            # Initialize crawling state
            all_results = []
            visited_urls: Set[str] = set()
            url_queue = [(url, 0) for url in start_urls]  # (url, depth)
            domain_counts: Dict[str, int] = {}
            
            max_depth = config.get('max_depth', 3)
            same_domain_only = config.get('same_domain_only', True)
            max_urls_per_domain = config.get('max_urls_per_domain', 100)
            
            # Setup memory adaptive dispatcher
            dispatcher = MemoryAdaptiveDispatcher(
                memory_threshold_percent=config.get('memory_threshold_percent', 70.0),
                check_interval=config.get('check_interval', 1.0),
                max_session_permit=config.get('max_concurrent', 10)
            )
            
            # Prepare crawler configuration
            crawler_config = CrawlerRunConfig(
                cache_mode=config.get('cache_mode', CacheMode.BYPASS),
                stream=config.get('stream', False)
            )
            
            # Process URLs level by level
            while url_queue:
                # Get URLs for current depth level
                current_level_urls = []
                remaining_queue = []
                
                for url, depth in url_queue:
                    if depth <= max_depth:
                        normalized_url = self._normalize_url(url)
                        if normalized_url not in visited_urls:
                            # Check domain limits
                            domain = self._get_domain(normalized_url)
                            if domain_counts.get(domain, 0) < max_urls_per_domain:
                                current_level_urls.append(normalized_url)
                                visited_urls.add(normalized_url)
                                domain_counts[domain] = domain_counts.get(domain, 0) + 1
                    else:
                        remaining_queue.append((url, depth))
                
                if not current_level_urls:
                    break
                
                # Crawl current level URLs
                level_results = await self._crawl_urls_batch(
                    current_level_urls, crawler_config, dispatcher
                )
                all_results.extend(level_results)
                
                # Extract links from successful results for next level
                if max_depth > 0:  # Only extract links if we haven't reached max depth
                    next_level_urls = []
                    for result in level_results:
                        if result.success and hasattr(result, 'metadata') and result.metadata:
                            links = self._extract_internal_links(result, same_domain_only)
                            for link in links:
                                # Add to queue with incremented depth
                                current_depth = next((d for u, d in url_queue if u == result.url), 0)
                                next_level_urls.append((link, current_depth + 1))
                    
                    url_queue = remaining_queue + next_level_urls
                else:
                    url_queue = remaining_queue
            
            return all_results
            
        except Exception as e:
            error_result = self._create_error_result("recursive", f"Recursive crawling failed: {str(e)}", "exception")
            return [error_result]
    
    async def _crawl_urls_batch(self, urls: List[str], crawler_config: CrawlerRunConfig, 
                               dispatcher: MemoryAdaptiveDispatcher) -> List[CrawlResult]:
        """Crawl a batch of URLs using the crawler instance."""
        try:
            crawl_configs = [crawler_config] * len(urls)
            results = await self.crawler_instance.arun_many(
                urls=urls,
                configs=crawl_configs,
                dispatcher=dispatcher
            )
            
            # Convert results
            crawl_results = []
            for i, (url, result) in enumerate(zip(urls, results)):
                crawl_result = await self._convert_result(url, result, i)
                crawl_results.append(crawl_result)
            
            return crawl_results
            
        except Exception as e:
            return [self._create_error_result(url, f"Batch crawl failed: {str(e)}", "batch_error") for url in urls]
    
    async def _convert_result(self, url: str, result: Any, index: int) -> CrawlResult:
        """Convert a crawl4ai result to a CrawlResult object."""
        try:
            if not result or not hasattr(result, 'success') or not result.success:
                error_msg = result.error_message if hasattr(result, 'error_message') else "Crawl failed"
                return self._create_error_result(url, error_msg, "crawl_failure")
            
            if not result.markdown:
                return self._create_error_result(url, "No content found", "empty_content")
            
            # Extract internal links for metadata
            internal_links = []
            if hasattr(result, 'internal_links') and result.internal_links:
                internal_links = result.internal_links
            
            metadata = {
                'recursive_index': index,
                'title': getattr(result, 'metadata', {}).get('title', '') if hasattr(result, 'metadata') else '',
                'url': url,
                'content_length': len(result.markdown) if result.markdown else 0,
                'internal_links_count': len(internal_links),
                'crawl_timestamp': getattr(result, 'timestamp', None) if hasattr(result, 'timestamp') else None
            }
            
            return self._create_success_result(
                url=url,
                content=result.markdown,
                markdown=result.markdown,
                metadata=metadata,
                urls_found=internal_links
            )
            
        except Exception as e:
            return self._create_error_result(url, f"Error processing result: {str(e)}", "processing_error")
    
    def _normalize_url(self, url: str) -> str:
        """Normalize URL by removing fragments and ensuring consistent format."""
        try:
            parsed = urlparse(url)
            # Remove fragment (anchor) and rebuild URL
            normalized = urlunparse((
                parsed.scheme,
                parsed.netloc,
                parsed.path,
                parsed.params,
                parsed.query,
                ''  # Remove fragment
            ))
            return normalized
        except Exception:
            return url
    
    def _get_domain(self, url: str) -> str:
        """Extract domain from URL."""
        try:
            return urlparse(url).netloc
        except Exception:
            return ''
    
    def _extract_internal_links(self, result: CrawlResult, same_domain_only: bool = True) -> List[str]:
        """Extract internal links from a crawl result."""
        internal_links = []
        
        try:
            if result.urls_found:
                base_domain = self._get_domain(result.url) if same_domain_only else None
                
                for link in result.urls_found:
                    # Normalize link
                    if link.startswith('/'):
                        # Relative URL - make absolute
                        link = urljoin(result.url, link)
                    
                    # Check domain restriction
                    if same_domain_only and base_domain:
                        link_domain = self._get_domain(link)
                        if link_domain != base_domain:
                            continue
                    
                    normalized_link = self._normalize_url(link)
                    if normalized_link and normalized_link not in internal_links:
                        internal_links.append(normalized_link)
            
        except Exception:
            pass  # Ignore link extraction errors
        
        return internal_links
    
    def get_crawl_summary(self, results: List[CrawlResult]) -> Dict[str, Any]:
        """Get summary statistics for a recursive crawl operation."""
        total = len(results)
        successful = len([r for r in results if r.success])
        failed = total - successful
        
        domains = set()
        total_links_found = 0
        
        for result in results:
            if result.success:
                domains.add(self._get_domain(result.url))
                if result.metadata and 'internal_links_count' in result.metadata:
                    total_links_found += result.metadata['internal_links_count']
        
        return {
            'total_urls_crawled': total,
            'successful': successful,
            'failed': failed,
            'success_rate': round((successful / total * 100) if total > 0 else 0, 2),
            'unique_domains': len(domains),
            'total_internal_links_found': total_links_found,
            'domains_crawled': list(domains)
        }