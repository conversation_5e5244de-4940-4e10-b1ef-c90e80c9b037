"""
Crawler manager for orchestrating and coordinating different crawler types.

This manager provides a unified interface for all crawler operations and handles
crawler selection, coordination, and resource management.
"""

from typing import Dict, Any, List, Optional, Union
import asyncio
from .base import BaseCrawler, CrawlResult, CrawlType, URLDetector
from .single_page import SinglePageCrawler
from .text_file import TextFileCrawler
from .sitemap import SitemapCrawler
from .batch import BatchCrawler
from .recursive import RecursiveCrawler
from .smart import SmartCrawler
from .robots_txt import RobotsTxtCrawler


class CrawlerManager:
    """
    Manager class for coordinating different crawler types and providing unified access.
    
    This manager follows the strategy pattern to provide a clean interface for
    all crawling operations while handling crawler selection and coordination.
    """
    
    def __init__(self, crawler_instance: Any = None, **default_config):
        """
        Initialize the crawler manager.
        
        Args:
            crawler_instance: The underlying crawl4ai crawler instance
            **default_config: Default configuration applied to all crawlers
        """
        self.crawler_instance = crawler_instance
        self.default_config = default_config
        
        # Initialize all crawler types
        self.crawlers = self._initialize_crawlers()
        
        # Smart crawler for intelligent routing
        self.smart_crawler = SmartCrawler(crawler_instance, **default_config)
        
        # Statistics tracking
        self.stats = {
            'total_crawls': 0,
            'successful_crawls': 0,
            'failed_crawls': 0,
            'crawls_by_type': {crawl_type.value: 0 for crawl_type in CrawlType}
        }
    
    def _initialize_crawlers(self) -> Dict[CrawlType, BaseCrawler]:
        """Initialize all crawler types with the provided crawler instance."""
        return {
            CrawlType.SINGLE_PAGE: SinglePageCrawler(self.crawler_instance, **self.default_config),
            CrawlType.TEXT_FILE: TextFileCrawler(self.crawler_instance, **self.default_config),
            CrawlType.SITEMAP: SitemapCrawler(self.crawler_instance, **self.default_config),
            CrawlType.BATCH: BatchCrawler(self.crawler_instance, **self.default_config),
            CrawlType.RECURSIVE: RecursiveCrawler(self.crawler_instance, **self.default_config),
            CrawlType.ROBOTS_TXT: RobotsTxtCrawler(self.crawler_instance, **self.default_config)
        }
    
    async def crawl(self, url: str, crawler_type: Optional[CrawlType] = None, **kwargs) -> CrawlResult:
        """
        Crawl a URL using the specified or auto-detected crawler type.
        
        Args:
            url: The URL to crawl
            crawler_type: Specific crawler type to use (optional, auto-detected if not provided)
            **kwargs: Additional configuration options
            
        Returns:
            CrawlResult: The result of the crawling operation
        """
        try:
            # Update statistics
            self.stats['total_crawls'] += 1
            
            # Merge default config with provided config
            config = self.default_config.copy()
            config.update(kwargs)
            
            if crawler_type is None:
                # Use smart crawler for automatic routing
                result = await self.smart_crawler.crawl(url, **config)
                detected_type = URLDetector.detect_url_type(url)
                self.stats['crawls_by_type'][detected_type.value] += 1
            else:
                # Use specific crawler type
                crawler = self.crawlers.get(crawler_type)
                if not crawler:
                    result = CrawlResult.from_error(url, f"Unsupported crawler type: {crawler_type}", "unsupported_type")
                else:
                    result = await crawler.crawl(url, **config)
                self.stats['crawls_by_type'][crawler_type.value] += 1
            
            # Update success/failure statistics
            if result.success:
                self.stats['successful_crawls'] += 1
            else:
                self.stats['failed_crawls'] += 1
            
            return result
            
        except Exception as e:
            self.stats['failed_crawls'] += 1
            return CrawlResult.from_error(url, f"Manager error: {str(e)}", "manager_error")
    
    async def crawl_batch(self, urls: List[str], **kwargs) -> List[CrawlResult]:
        """
        Crawl multiple URLs using batch processing.
        
        Args:
            urls: List of URLs to crawl
            **kwargs: Additional configuration options
            
        Returns:
            List[CrawlResult]: Results for each URL
        """
        if not urls:
            return []
        
        try:
            config = self.default_config.copy()
            config.update(kwargs)
            
            batch_crawler = self.crawlers[CrawlType.BATCH]
            results = await batch_crawler.crawl_batch(urls, **config)
            
            # Update statistics
            self.stats['total_crawls'] += len(urls)
            self.stats['crawls_by_type'][CrawlType.BATCH.value] += len(urls)
            
            for result in results:
                if result.success:
                    self.stats['successful_crawls'] += 1
                else:
                    self.stats['failed_crawls'] += 1
            
            return results
            
        except Exception as e:
            self.stats['failed_crawls'] += len(urls)
            error_result = CrawlResult.from_error("batch", f"Batch crawl error: {str(e)}", "batch_error")
            return [error_result] * len(urls)
    
    async def crawl_recursive(self, start_urls: List[str], **kwargs) -> List[CrawlResult]:
        """
        Crawl URLs recursively following internal links.
        
        Args:
            start_urls: List of starting URLs
            **kwargs: Additional configuration options including max_depth
            
        Returns:
            List[CrawlResult]: Results for all crawled URLs
        """
        if not start_urls:
            return []
        
        try:
            config = self.default_config.copy()
            config.update(kwargs)
            
            recursive_crawler = self.crawlers[CrawlType.RECURSIVE]
            results = await recursive_crawler.crawl_recursive(start_urls, **config)
            
            # Update statistics
            self.stats['total_crawls'] += len(results)
            self.stats['crawls_by_type'][CrawlType.RECURSIVE.value] += len(results)
            
            for result in results:
                if result.success:
                    self.stats['successful_crawls'] += 1
                else:
                    self.stats['failed_crawls'] += 1
            
            return results
            
        except Exception as e:
            self.stats['failed_crawls'] += len(start_urls)
            error_result = CrawlResult.from_error("recursive", f"Recursive crawl error: {str(e)}", "recursive_error")
            return [error_result]
    
    async def smart_crawl(self, url: str, **kwargs) -> CrawlResult:
        """
        Perform intelligent crawling with automatic URL type detection and routing.
        
        Args:
            url: The URL to crawl
            **kwargs: Additional configuration options
            
        Returns:
            CrawlResult: The result of the smart crawling operation
        """
        return await self.crawl(url, crawler_type=None, **kwargs)
    
    def get_crawler_for_url(self, url: str) -> BaseCrawler:
        """
        Get the appropriate crawler for a given URL.
        
        Args:
            url: The URL to analyze
            
        Returns:
            BaseCrawler: The appropriate crawler instance
        """
        url_type = URLDetector.detect_url_type(url)
        return self.crawlers.get(url_type, self.crawlers[CrawlType.SINGLE_PAGE])
    
    def get_url_routing_info(self, url: str) -> Dict[str, Any]:
        """
        Get detailed routing information for a URL.
        
        Args:
            url: The URL to analyze
            
        Returns:
            Dict[str, Any]: Routing information including detected type and crawler
        """
        return self.smart_crawler.get_routing_info(url)
    
    def check_robots_permission(self, url: str, user_agent: str = '*') -> Dict[str, Any]:
        """
        Check robots.txt permissions for a URL.
        
        Args:
            url: The URL to check
            user_agent: User agent to check permissions for
            
        Returns:
            Dict[str, Any]: Permission information
        """
        try:
            robots_crawler = self.crawlers[CrawlType.ROBOTS_TXT]
            robots_url = robots_crawler.get_robots_txt_url(url)
            
            return {
                'url': url,
                'robots_txt_url': robots_url,
                'user_agent': user_agent,
                'check_required': True,
                'note': 'Use crawl() with robots_txt_url to get actual permissions'
            }
        except Exception as e:
            return {
                'url': url,
                'error': str(e),
                'check_required': False
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get crawler manager statistics.
        
        Returns:
            Dict[str, Any]: Usage statistics
        """
        total = self.stats['total_crawls']
        success_rate = (self.stats['successful_crawls'] / total * 100) if total > 0 else 0
        
        return {
            'total_crawls': total,
            'successful_crawls': self.stats['successful_crawls'],
            'failed_crawls': self.stats['failed_crawls'],
            'success_rate': round(success_rate, 2),
            'crawls_by_type': self.stats['crawls_by_type'].copy(),
            'available_crawlers': list(self.crawlers.keys())
        }
    
    def reset_statistics(self):
        """Reset crawler manager statistics."""
        self.stats = {
            'total_crawls': 0,
            'successful_crawls': 0,
            'failed_crawls': 0,
            'crawls_by_type': {crawl_type.value: 0 for crawl_type in CrawlType}
        }
    
    def get_crawler_config(self, crawler_type: CrawlType) -> Dict[str, Any]:
        """
        Get the default configuration for a specific crawler type.
        
        Args:
            crawler_type: The crawler type to get config for
            
        Returns:
            Dict[str, Any]: Default configuration for the crawler
        """
        crawler = self.crawlers.get(crawler_type)
        if crawler:
            return crawler.get_default_config()
        return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check on the crawler manager and its components.
        
        Returns:
            Dict[str, Any]: Health check results
        """
        health = {
            'status': 'healthy',
            'crawler_instance': self.crawler_instance is not None,
            'available_crawlers': len(self.crawlers),
            'smart_crawler': self.smart_crawler is not None,
            'issues': []
        }
        
        if not self.crawler_instance:
            health['status'] = 'degraded'
            health['issues'].append('No crawler instance provided')
        
        # Check each crawler can be instantiated
        for crawl_type, crawler in self.crawlers.items():
            if not crawler:
                health['status'] = 'degraded'
                health['issues'].append(f'Crawler {crawl_type.value} not available')
        
        return health