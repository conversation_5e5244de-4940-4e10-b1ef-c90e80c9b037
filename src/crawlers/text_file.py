"""
Text file crawler for .txt and .md file handling.

This crawler handles text and markdown files, corresponding to the 
original crawl_markdown_file function from the monolithic implementation.
"""

from typing import Dict, Any
from crawl4ai import CrawlerRunConfig
from .base import BaseCrawler, CrawlResult, CrawlType, URLDetector


class TextFileCrawler(BaseCrawler):
    """Crawler for text and markdown file operations."""
    
    @property
    def crawler_type(self) -> CrawlType:
        """Return the crawler type."""
        return CrawlType.TEXT_FILE
    
    def can_handle(self, url: str) -> bool:
        """
        Check if this crawler can handle the given URL.
        
        Handles .txt, .md, and .markdown files.
        """
        return URLDetector.is_text_file(url)
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for text file crawling."""
        return {
            'stream': False,
            'extract_content': True
        }
    
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """
        Crawl a text or markdown file.
        
        Args:
            url: The URL to the text/markdown file
            **kwargs: Additional configuration options
                - extract_content: Whether to extract and process content (default: True)
                - Any other CrawlerRunConfig parameters
                
        Returns:
            CrawlResult: The result of the crawling operation
        """
        if not await self.validate_url(url):
            return self._create_error_result(url, "Invalid URL format", "validation_error")
        
        if not self.can_handle(url):
            return self._create_error_result(url, "URL is not a text or markdown file", "unsupported_format")
        
        if not self.crawler_instance:
            return self._create_error_result(url, "Crawler instance not provided", "configuration_error")
        
        try:
            # Prepare crawler configuration with defaults for text files
            config_options = self.get_default_config()
            config_options.update(kwargs)
            
            # Create crawler config (text files use default config)
            crawler_config = CrawlerRunConfig()
            
            # Perform the crawl
            result = await self.crawler_instance.arun(
                url=url,
                config=crawler_config
            )
            
            # Check if crawl was successful
            if not result.success:
                error_msg = result.error_message if hasattr(result, 'error_message') else "Failed to crawl text file"
                return self._create_error_result(url, error_msg, "crawl_failure")
            
            # For text files, we expect to get raw content
            content = None
            markdown = None
            
            if hasattr(result, 'raw_html') and result.raw_html:
                content = result.raw_html
                markdown = result.raw_html  # For text files, raw content is the markdown
            elif hasattr(result, 'markdown') and result.markdown:
                content = result.markdown
                markdown = result.markdown
            elif hasattr(result, 'text') and result.text:
                content = result.text
                markdown = result.text
            
            if not content:
                return self._create_error_result(url, "No content found in text file", "empty_content")
            
            # Detect file type from URL
            file_extension = self._get_file_extension(url)
            
            # Create metadata
            metadata = {
                'file_type': file_extension,
                'url': url,
                'content_length': len(content),
                'is_markdown': file_extension in ('.md', '.markdown'),
                'crawl_timestamp': getattr(result, 'timestamp', None) if hasattr(result, 'timestamp') else None
            }
            
            return self._create_success_result(
                url=url,
                content=content,
                markdown=markdown,
                metadata=metadata
            )
            
        except Exception as e:
            return self._create_error_result(url, f"Error crawling text file: {str(e)}", "exception")
    
    def _get_file_extension(self, url: str) -> str:
        """Extract file extension from URL."""
        try:
            from urllib.parse import urlparse
            import os
            path = urlparse(url).path
            return os.path.splitext(path)[1]
        except Exception:
            return '.txt'  # Default fallback