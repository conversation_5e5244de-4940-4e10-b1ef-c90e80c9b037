"""Modular MCP server creation for Crawl4AI."""

import async<PERSON>
from contextlib import asynccontextmanager
from typing import Op<PERSON>, AsyncIterator

from mcp.server.fastmcp import FastMCP, Context

from src.config.settings import Settings
from src.core.application_context import ApplicationContext
from src.tools import register_all_tools


@asynccontextmanager
async def modular_lifespan(context: ApplicationContext) -> AsyncIterator[dict]:
    """
    Lifespan context manager for the modular server.
    
    Handles initialization and cleanup of the application context.
    """
    # Initialize context
    await context.initialize()
    
    # Yield context for server use
    yield {"context": context}
    
    # Cleanup on shutdown
    await context.cleanup()


def create_server(settings: Settings, context: Optional[ApplicationContext] = None, transport: str = "stdio") -> FastMCP:
    """
    Create and configure the MCP server with modular architecture.

    Args:
        settings: Application settings
        context: Optional pre-configured application context
        transport: Transport type to determine if host/port are needed

    Returns:
        Configured FastMCP server instance
    """
    # Create context if not provided
    if context is None:
        context = ApplicationContext(settings)

    # Create FastMCP server with lifespan
    # Only pass host/port for non-stdio transports
    if transport == "stdio":
        server = FastMCP(
            name="crawl4ai-mcp",
            description="MCP server for web crawling and RAG functionality",
            lifespan=lambda app: modular_lifespan(context)
        )
    else:
        server = FastMCP(
            name="crawl4ai-mcp",
            description="MCP server for web crawling and RAG functionality",
            lifespan=lambda app: modular_lifespan(context),
            host=settings.server.host,
            port=settings.server.port
        )

    # Register all tools
    register_all_tools(server, context)

    return server


def run_server(server: FastMCP, transport: str = "stdio"):
    """
    Run the server with the specified transport.

    Args:
        server: The FastMCP server instance
        transport: Transport type ("sse" or "stdio") - "http" is mapped to "sse"
    """
    if transport == "http" or transport == "streamable-http" or transport == "sse":
        # Use SSE transport for HTTP requests (this version doesn't have streamable HTTP)
        asyncio.run(server.run_sse_async())
    elif transport == "stdio":
        asyncio.run(server.run_stdio_async())
    else:
        raise ValueError(f"Unknown transport: {transport}")