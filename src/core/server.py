"""MCP server creation and configuration for Crawl4AI."""

import logging
from typing import Optional, Callable, Any

from mcp.server.fastmcp import FastMCP

from src.config.settings import Settings, get_settings
from src.core.context import crawl4ai_lifespan

logger = logging.getLogger(__name__)


def create_mcp_server(
    name: str = "mcp-crawl4ai-rag",
    description: str = "MCP server for RAG and web crawling with Crawl4AI",
    host: Optional[str] = None,
    port: Optional[int] = None
) -> FastMCP:
    """
    Create and configure the MCP server.
    
    This function creates the FastMCP server instance with the appropriate
    lifecycle management and configuration. Tools should be registered
    separately after server creation.
    
    Args:
        name: Server name for identification
        description: Human-readable server description
        host: Optional host override (defaults to settings)
        port: Optional port override (defaults to settings)
        
    Returns:
        FastMCP: Configured server instance ready for tool registration
    """
    # Get settings
    settings = get_settings()
    
    # Use provided values or fall back to settings
    server_host = host or settings.server.host
    server_port = port or settings.server.port
    
    logger.info(f"Creating MCP server '{name}' on {server_host}:{server_port}")
    
    # Create the server with lifecycle management
    server = FastMCP(
        name=name,
        description=description,
        lifespan=crawl4ai_lifespan,
        host=server_host,
        port=server_port
    )
    
    logger.info(f"✓ MCP server created successfully")
    
    return server


def register_all_tools(server: FastMCP, context: Any = None) -> None:
    """
    Register all MCP tools with the server.
    
    This function imports and registers all tool modules. It's separated
    from server creation to allow for flexible tool registration and to
    avoid circular imports.
    
    Args:
        server: The FastMCP server instance to register tools with
        context: Application context containing shared resources
    """
    logger.info("Registering MCP tools...")
    
    try:
        # Import the main tool registration function
        from src.tools import register_all_tools as register_tools
        
        # Get context from server state if not provided
        if context is None:
            context = getattr(server, 'state', None)
        
        if context is None:
            logger.warning("No context available for tool registration")
            logger.warning("Tools may not function properly without application context")
            return
        
        # Register all tools with context
        register_tools(server, context)
        logger.info("✓ All MCP tools registered successfully")
        logger.info("  - 2 crawling tools (crawl_single_page, smart_crawl_url)")
        logger.info("  - 3 RAG tools (get_available_sources, perform_rag_query, search_code_examples)")
        logger.info("  - 3 knowledge graph tools (check_ai_script_hallucinations, query_knowledge_graph, parse_github_repository)")
        
    except ImportError as e:
        logger.error(f"Tool registration failed: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during tool registration: {e}")
        raise


def create_and_configure_server(
    register_tools: bool = True,
    context: Any = None,
    **kwargs
) -> FastMCP:
    """
    Create a fully configured MCP server with all tools registered.
    
    This is a convenience function that combines server creation and
    tool registration for the common use case.
    
    Args:
        register_tools: Whether to automatically register all tools
        context: Application context for tool registration
        **kwargs: Additional arguments passed to create_mcp_server
        
    Returns:
        FastMCP: Fully configured and ready-to-run server
    """
    # Create the server
    server = create_mcp_server(**kwargs)
    
    # Register tools if requested
    if register_tools:
        register_all_tools(server, context)
    
    return server