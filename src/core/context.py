"""Application context and lifecycle management for Crawl4AI MCP server."""

from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass
from typing import Optional, Any, TYPE_CHECKING
import logging

from mcp.server.fastmcp import FastMCP
from sentence_transformers import CrossEncoder
from supabase import Client
from crawl4ai import AsyncWebCrawler, BrowserConfig

from src.config.settings import Settings
from src.utils import get_supabase_client

# Type checking imports to avoid circular dependencies
if TYPE_CHECKING:
    from strategies.manager import StrategyManager

logger = logging.getLogger(__name__)


@dataclass
class Crawl4AIContext:
    """
    Application context for the Crawl4AI MCP server.
    
    This context holds all the major components needed by the server,
    including the crawler, database client, AI models, and configuration.
    Uses dependency injection pattern for lazy loading of components.
    """
    crawler: AsyncWebCrawler
    supabase_client: Client
    settings: Settings
    reranking_model: Optional[CrossEncoder] = None
    knowledge_validator: Optional[Any] = None  # KnowledgeGraphValidator when available
    repo_extractor: Optional[Any] = None       # DirectNeo4jExtractor when available
    script_analyzer: Optional[Any] = None      # AIScriptAnalyzer when available
    
    # Private fields for lazy-loaded components
    _strategy_manager: Optional['StrategyManager'] = None
    _crawler_manager: Optional[Any] = None  # CrawlerManager from crawlers module
    
    def get_strategy_manager(self) -> 'StrategyManager':
        """
        Lazy load strategy manager to avoid circular imports.
        
        Returns:
            StrategyManager: The initialized strategy manager
        """
        if self._strategy_manager is None:
            from strategies.manager import StrategyManager
            self._strategy_manager = StrategyManager(self.settings, self)
        return self._strategy_manager
    
    def get_crawler_manager(self):
        """
        Lazy load crawler manager to avoid circular imports.
        
        Returns:
            CrawlerManager: The initialized crawler manager
        """
        if self._crawler_manager is None:
            from crawlers import CrawlerManager
            self._crawler_manager = CrawlerManager(self.crawler)
        return self._crawler_manager
    
    @property
    def strategy_manager(self) -> 'StrategyManager':
        """Property accessor for strategy manager."""
        return self.get_strategy_manager()
    
    @property
    def crawler_manager(self):
        """Property accessor for crawler manager."""
        return self.get_crawler_manager()
    
    async def close(self):
        """Clean up resources when shutting down."""
        # Close strategy manager if initialized
        if self._strategy_manager:
            await self._strategy_manager.close()
        
        # Close knowledge graph components
        if self.knowledge_validator:
            try:
                await self.knowledge_validator.close()
                logger.info("Knowledge graph validator closed")
            except Exception as e:
                logger.error(f"Error closing knowledge validator: {e}")
        
        if self.repo_extractor:
            try:
                await self.repo_extractor.close()
                logger.info("Repository extractor closed")
            except Exception as e:
                logger.error(f"Error closing repository extractor: {e}")


async def _initialize_reranking_model(settings: Settings) -> Optional[CrossEncoder]:
    """
    Initialize the reranking model if enabled.
    
    Args:
        settings: Application settings
        
    Returns:
        CrossEncoder instance or None if disabled/failed
    """
    if not settings.strategies.use_reranking:
        logger.info("Reranking disabled in settings")
        return None
    
    try:
        # Try different models based on performance requirements
        model_name = "cross-encoder/ms-marco-MiniLM-L-6-v2"
        if settings.performance and hasattr(settings.performance, 'use_tiny_model'):
            model_name = "cross-encoder/ms-marco-TinyBERT-L-2-v2"
        
        model = CrossEncoder(model_name)
        logger.info(f"Reranking model loaded successfully: {model_name}")
        return model
    except Exception as e:
        logger.error(f"Failed to load reranking model: {e}")
        logger.warning("Continuing without reranking support")
        return None


async def _initialize_knowledge_graph_components(settings: Settings) -> tuple[Optional[Any], Optional[Any], Optional[Any]]:
    """
    Initialize Neo4j knowledge graph components if enabled and configured.
    
    Args:
        settings: Application settings
        
    Returns:
        Tuple of (knowledge_validator, repo_extractor, script_analyzer) or (None, None, None) if disabled/failed
    """
    if not settings.strategies.use_knowledge_graph:
        logger.info("Knowledge graph functionality disabled in settings")
        return None, None, None
    
    if not settings.neo4j:
        logger.warning("Knowledge graph enabled but Neo4j not configured")
        logger.warning("Set NEO4J_URI, NEO4J_USER, and NEO4J_PASSWORD to enable")
        return None, None, None
    
    try:
        # Import here to avoid circular dependencies and handle missing imports gracefully
        import sys
        from pathlib import Path
        
        # Add knowledge_graphs folder to path
        knowledge_graphs_path = Path(__file__).resolve().parent.parent.parent / 'knowledge_graphs'
        sys.path.append(str(knowledge_graphs_path))
        
        from knowledge_graph_validator import KnowledgeGraphValidator
        from parse_repo_into_neo4j import DirectNeo4jExtractor
        from ai_script_analyzer import AIScriptAnalyzer
        
        neo4j_uri = settings.neo4j.uri
        neo4j_user = settings.neo4j.user
        neo4j_password = settings.neo4j.password.get_secret_value()
        
        logger.info("Initializing knowledge graph components...")
        
        # Initialize knowledge graph validator
        knowledge_validator = KnowledgeGraphValidator(neo4j_uri, neo4j_user, neo4j_password)
        
        # Test the connection
        test_result = knowledge_validator.test_connection()
        if not test_result:
            logger.warning("Neo4j connection test failed, knowledge graph features disabled")
            return None, None, None
        
        logger.info("✓ Knowledge graph validator initialized")
        
        # Initialize repository extractor
        repo_extractor = DirectNeo4jExtractor(neo4j_uri, neo4j_user, neo4j_password)
        logger.info("✓ Repository extractor initialized")
        
        # Initialize AI script analyzer
        script_analyzer = AIScriptAnalyzer(knowledge_validator)
        logger.info("✓ AI script analyzer initialized")
        
        return knowledge_validator, repo_extractor, script_analyzer
        
    except Exception as e:
        # Format Neo4j errors for better user experience
        error_msg = _format_neo4j_error(e)
        logger.error(f"Failed to initialize Neo4j components: {error_msg}")
        logger.warning("Continuing without knowledge graph support")
        return None, None, None


def _format_neo4j_error(error: Exception) -> str:
    """Format Neo4j connection errors for user-friendly messages."""
    error_str = str(error).lower()
    if "authentication" in error_str or "unauthorized" in error_str:
        return "Neo4j authentication failed. Check NEO4J_USER and NEO4J_PASSWORD."
    elif "connection" in error_str or "refused" in error_str or "timeout" in error_str:
        return "Cannot connect to Neo4j. Check NEO4J_URI and ensure Neo4j is running."
    elif "database" in error_str:
        return "Neo4j database error. Check if the database exists and is accessible."
    else:
        return f"Neo4j error: {str(error)}"


@asynccontextmanager
async def crawl4ai_lifespan(server: FastMCP) -> AsyncIterator[Crawl4AIContext]:
    """
    Manages the Crawl4AI application lifecycle.
    
    This context manager initializes all components needed by the server,
    yields the application context, and ensures proper cleanup on shutdown.
    
    Args:
        server: The FastMCP server instance
        
    Yields:
        Crawl4AIContext: The initialized application context
    """
    # Load settings
    settings = get_settings()
    
    # Create browser configuration
    browser_config = BrowserConfig(
        headless=True,
        verbose=False
    )
    
    # Initialize the crawler
    logger.info("Initializing AsyncWebCrawler...")
    crawler = AsyncWebCrawler(config=browser_config)
    await crawler.__aenter__()
    logger.info("✓ AsyncWebCrawler initialized")
    
    # Initialize Supabase client
    logger.info("Initializing Supabase client...")
    supabase_client = None
    try:
        supabase_client = get_supabase_client()
        logger.info("✓ Supabase client initialized")
    except ValueError as e:
        logger.warning(f"Supabase not configured: {e}")
        logger.warning("Continuing without database support")
    
    # Initialize optional components
    reranking_model = await _initialize_reranking_model(settings)
    knowledge_validator, repo_extractor, script_analyzer = await _initialize_knowledge_graph_components(settings)
    
    # Create context with initialized components
    ctx = Crawl4AIContext(
        crawler=crawler,
        supabase_client=supabase_client,
        settings=settings,
        reranking_model=reranking_model,
        knowledge_validator=knowledge_validator,
        repo_extractor=repo_extractor,
        script_analyzer=script_analyzer
    )
    
    try:
        yield ctx
    finally:
        # Clean up all components
        logger.info("Shutting down Crawl4AI server...")
        
        # Clean up context resources
        await ctx.close()
        
        # Clean up crawler
        await crawler.__aexit__(None, None, None)
        logger.info("✓ AsyncWebCrawler closed")
        
        logger.info("✓ Crawl4AI server shutdown complete")