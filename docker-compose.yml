version: '3.8'

services:
  crawl4ai-mcp:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        PORT: 8051
    ports:
      - "8051:8051"
    environment:
      - HOST=0.0.0.0
      - PORT=8051
      - TRANSPORT=sse
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_CHOICE=${MODEL_CHOICE:-gpt-4-mini}
      - USE_CONTEXTUAL_EMBEDDINGS=${USE_CONTEXTUAL_EMBEDDINGS:-false}
      - USE_HYBRID_SEARCH=${USE_HYBRID_SEARCH:-true}
      - USE_AGENTIC_RAG=${USE_AGENTIC_RAG:-false}
      - USE_RERANKING=${USE_RERANKING:-false}
      - USE_KNOWLEDGE_GRAPH=${USE_KNOWLEDGE_GRAPH:-false}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - NEO4J_URI=${NEO4J_URI:-bolt://neo4j:7687}
      - NEO4J_USER=${NEO4J_USER:-neo4j}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
    depends_on:
      - neo4j
    networks:
      - crawl4ai-network

  # Optional: Neo4j for knowledge graph functionality
  neo4j:
    image: neo4j:latest
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password123  # Change this!
      - NEO4J_PLUGINS=["apoc"]
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    networks:
      - crawl4ai-network

  # Legacy version (if needed)
  crawl4ai-mcp-legacy:
    build:
      context: .
      dockerfile: Dockerfile.legacy
      args:
        PORT: 8052
    ports:
      - "8052:8052"
    environment:
      - HOST=0.0.0.0
      - PORT=8052
      - TRANSPORT=sse
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
    profiles:
      - legacy
    networks:
      - crawl4ai-network

networks:
  crawl4ai-network:
    driver: bridge

volumes:
  neo4j_data:
  neo4j_logs: