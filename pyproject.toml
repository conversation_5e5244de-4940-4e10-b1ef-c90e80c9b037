[project]
name = "crawl4ai-mcp"
version = "0.1.0"
description = "MCP server for integrating web crawling and RAG into AI agents and AI coding assistants"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "crawl4ai==0.6.2",
    "mcp==1.7.1",
    "supabase==2.15.1",
    "openai==1.71.0",
    "dotenv==0.9.9",
    "sentence-transformers>=4.1.0",
    "neo4j>=5.28.1",
]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "pytest-mock>=3.14.0",
]
