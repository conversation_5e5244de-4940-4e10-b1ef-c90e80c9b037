#!/usr/bin/env python3
"""Test script for stdio MCP server."""

import subprocess
import json
import sys
import os
import time

def test_mcp_server():
    """Test the MCP server with stdio transport."""
    
    # Set environment
    env = os.environ.copy()
    env['TRANSPORT'] = 'stdio'
    env['PYTHONPATH'] = '.'
    
    print("Starting MCP server...")
    
    # Start the server process
    proc = subprocess.Popen(
        [sys.executable, 'src/main.py'],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        env=env
    )
    
    # Wait a moment for server to start
    time.sleep(2)
    
    # Send initialize message
    init_msg = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        }
    }
    
    print("Sending initialize message...")
    print(json.dumps(init_msg, indent=2))
    
    try:
        # Send the message
        stdout, stderr = proc.communicate(
            input=json.dumps(init_msg) + '\n',
            timeout=10
        )
        
        print("\n=== STDOUT ===")
        print(stdout)
        print("\n=== STDERR ===")
        print(stderr)
        print(f"\n=== Return Code: {proc.returncode} ===")
        
        if stdout:
            try:
                response = json.loads(stdout.strip())
                print("\n=== Parsed Response ===")
                print(json.dumps(response, indent=2))
            except json.JSONDecodeError:
                print("Response is not valid JSON")
        
    except subprocess.TimeoutExpired:
        print("Process timed out")
        proc.kill()
        stdout, stderr = proc.communicate()
        print("STDOUT after kill:", stdout)
        print("STDERR after kill:", stderr)

if __name__ == "__main__":
    test_mcp_server()
