"""Unit tests for the main entry point."""

import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import sys
from src.main import main


class TestMain:
    """Test the main entry point."""
    
    def test_main_with_valid_config(self):
        """Test main with valid configuration."""
        with patch('src.main.Settings') as mock_settings_class:
            with patch('src.main.create_server') as mock_create_server:
                with patch('src.main.run_server') as mock_run_server:
                    # Setup mocks
                    mock_settings = MagicMock()
                    mock_validation = MagicMock()
                    mock_validation.is_valid = True
                    mock_validation.errors = []
                    mock_validation.warnings = []
                    mock_settings.validate.return_value = mock_validation
                    mock_settings.get_health_report.return_value = {
                        'health': 'healthy',
                        'is_valid': True
                    }
                    mock_settings.server.host = "0.0.0.0"
                    mock_settings.server.port = 8051
                    mock_settings_class.return_value = mock_settings
                    
                    mock_server = MagicMock()
                    mock_create_server.return_value = mock_server
                    
                    # Run main
                    main()
                    
                    # Verify calls
                    mock_settings.validate.assert_called_once()
                    mock_settings.get_health_report.assert_called_once()
                    mock_create_server.assert_called_once_with(mock_settings)
                    mock_run_server.assert_called_once_with(mock_server, "sse")
    
    def test_main_with_invalid_config(self):
        """Test main with invalid configuration."""
        with patch('src.main.Settings') as mock_settings_class:
            with patch('src.main.sys.exit') as mock_exit:
                # Setup mocks
                mock_settings = MagicMock()
                mock_validation = MagicMock()
                mock_validation.is_valid = False
                mock_validation.errors = ["Missing SUPABASE_URL", "Missing OPENAI_API_KEY"]
                mock_validation.warnings = []
                mock_settings.validate.return_value = mock_validation
                mock_settings_class.return_value = mock_settings
                
                # Run main
                main()
                
                # Verify exit was called
                mock_exit.assert_called_once_with(1)
    
    def test_main_with_warnings(self):
        """Test main with configuration warnings."""
        with patch('src.main.Settings') as mock_settings_class:
            with patch('src.main.create_server') as mock_create_server:
                with patch('src.main.run_server') as mock_run_server:
                    with patch('src.main.logger') as mock_logger:
                        # Setup mocks
                        mock_settings = MagicMock()
                        mock_validation = MagicMock()
                        mock_validation.is_valid = True
                        mock_validation.errors = []
                        mock_validation.warnings = ["High concurrent crawl limit"]
                        mock_settings.validate.return_value = mock_validation
                        mock_settings.get_health_report.return_value = {
                            'health': 'degraded',
                            'is_valid': True
                        }
                        mock_settings.server.host = "0.0.0.0"
                        mock_settings.server.port = 8051
                        mock_settings_class.return_value = mock_settings
                        
                        mock_server = MagicMock()
                        mock_create_server.return_value = mock_server
                        
                        # Run main
                        main()
                        
                        # Verify warning was logged
                        mock_logger.warning.assert_called()
                        mock_run_server.assert_called_once()
    
    def test_main_keyboard_interrupt(self):
        """Test handling keyboard interrupt."""
        with patch('src.main.Settings') as mock_settings_class:
            with patch('src.main.create_server') as mock_create_server:
                with patch('src.main.run_server') as mock_run_server:
                    with patch('src.main.logger') as mock_logger:
                        # Setup mocks
                        mock_settings = MagicMock()
                        mock_validation = MagicMock()
                        mock_validation.is_valid = True
                        mock_validation.errors = []
                        mock_validation.warnings = []
                        mock_settings.validate.return_value = mock_validation
                        mock_settings.get_health_report.return_value = {
                            'health': 'healthy',
                            'is_valid': True
                        }
                        mock_settings.server.host = "0.0.0.0"
                        mock_settings.server.port = 8051
                        mock_settings_class.return_value = mock_settings
                        
                        mock_server = MagicMock()
                        mock_create_server.return_value = mock_server
                        mock_run_server.side_effect = KeyboardInterrupt()
                        
                        # Run main - should handle KeyboardInterrupt gracefully
                        main()
                        
                        # Verify info message was logged
                        mock_logger.info.assert_any_call("Server shutdown requested")
    
    def test_main_server_error(self):
        """Test handling server errors."""
        with patch('src.main.Settings') as mock_settings_class:
            with patch('src.main.create_server') as mock_create_server:
                with patch('src.main.sys.exit') as mock_exit:
                    # Setup mocks
                    mock_settings = MagicMock()
                    mock_validation = MagicMock()
                    mock_validation.is_valid = True
                    mock_validation.errors = []
                    mock_validation.warnings = []
                    mock_settings.validate.return_value = mock_validation
                    mock_settings.get_health_report.return_value = {
                        'health': 'healthy',
                        'is_valid': True
                    }
                    mock_settings_class.return_value = mock_settings
                    
                    # Make create_server raise an error
                    mock_create_server.side_effect = Exception("Server creation failed")
                    
                    # Run main
                    main()
                    
                    # Verify exit was called
                    mock_exit.assert_called_once_with(1)