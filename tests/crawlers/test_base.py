"""Tests for base crawler functionality."""

import pytest
from unittest.mock import MagicMock

from src.crawlers.base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlError, CrawlType, URLDetector


class TestCrawlResult:
    """Test the CrawlResult class."""
    
    def test_initialization_minimal(self):
        """Test CrawlResult with minimal parameters."""
        result = CrawlResult(
            success=True,
            url="https://example.com"
        )
        
        assert result.success is True
        assert result.url == "https://example.com"
        assert result.content is None
        assert result.markdown is None
        assert result.crawl_type is None
        assert result.metadata is None
        assert result.urls_found is None
        assert result.error is None
    
    def test_initialization_full(self):
        """Test CrawlResult with all parameters."""
        error = CrawlError(
            url="https://example.com",
            error_message="Test error",
            error_type="test"
        )
        
        result = CrawlResult(
            success=False,
            url="https://example.com",
            content="Test content",
            markdown="# Test",
            crawl_type=CrawlType.SINGLE_PAGE,
            metadata={"key": "value"},
            urls_found=["https://example.com/page1"],
            error=error
        )
        
        assert result.success is False
        assert result.url == "https://example.com"
        assert result.content == "Test content"
        assert result.markdown == "# Test"
        assert result.crawl_type == CrawlType.SINGLE_PAGE
        assert result.metadata == {"key": "value"}
        assert result.urls_found == ["https://example.com/page1"]
        assert result.error == error
    
    def test_to_dict(self):
        """Test converting result to dictionary."""
        result = CrawlResult(
            success=True,
            url="https://example.com",
            content="Test content"
        )
        
        result_dict = result.to_dict()
        
        assert isinstance(result_dict, dict)
        assert result_dict["success"] is True
        assert result_dict["url"] == "https://example.com"
        assert result_dict["content"] == "Test content"
    
    def test_to_json(self):
        """Test converting result to JSON."""
        result = CrawlResult(
            success=True,
            url="https://example.com",
            content="Test content"
        )
        
        json_str = result.to_json()
        
        assert isinstance(json_str, str)
        assert "https://example.com" in json_str
        assert "Test content" in json_str


class TestCrawlError:
    """Test the CrawlError class."""
    
    def test_initialization(self):
        """Test CrawlError initialization."""
        error = CrawlError(
            url="https://example.com",
            error_message="Test error",
            error_type="test"
        )
        
        assert error.url == "https://example.com"
        assert error.error_message == "Test error"
        assert error.error_type == "test"
    
    def test_str_representation(self):
        """Test string representation of error."""
        error = CrawlError(
            url="https://example.com",
            error_message="Test error",
            error_type="test"
        )
        
        error_str = str(error)
        assert "https://example.com" in error_str
        assert "Test error" in error_str


class TestURLDetector:
    """Test the URLDetector class."""
    
    def test_webpage_detection(self):
        """Test detection of regular web pages."""
        detector = URLDetector()

        url_type = detector.detect_url_type("https://example.com")
        assert url_type == CrawlType.SINGLE_PAGE

        url_type = detector.detect_url_type("https://example.com/page.html")
        assert url_type == CrawlType.SINGLE_PAGE

    def test_sitemap_detection(self):
        """Test detection of sitemap URLs."""
        detector = URLDetector()

        url_type = detector.detect_url_type("https://example.com/sitemap.xml")
        assert url_type == CrawlType.SITEMAP

    def test_robots_txt_detection(self):
        """Test detection of robots.txt URLs."""
        detector = URLDetector()

        url_type = detector.detect_url_type("https://example.com/robots.txt")
        assert url_type == CrawlType.ROBOTS_TXT

    def test_text_file_detection(self):
        """Test detection of text files."""
        detector = URLDetector()

        url_type = detector.detect_url_type("https://example.com/readme.txt")
        assert url_type == CrawlType.TEXT_FILE

        url_type = detector.detect_url_type("https://example.com/doc.md")
        assert url_type == CrawlType.TEXT_FILE

    def test_unknown_detection(self):
        """Test detection of unknown URL types."""
        detector = URLDetector()

        url_type = detector.detect_url_type("https://example.com/file.unknown")
        assert url_type == CrawlType.SINGLE_PAGE  # Default fallback
