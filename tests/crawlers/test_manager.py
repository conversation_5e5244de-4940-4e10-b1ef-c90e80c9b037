"""Tests for CrawlerManager."""

import pytest
from unittest.mock import <PERSON><PERSON>ock, patch

from src.crawlers.manager import Crawler<PERSON><PERSON><PERSON>
from src.crawlers.base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlError, CrawlType


class TestCrawlerManager:
    """Test the CrawlerManager class."""
    
    def test_initialization(self):
        """Test CrawlerManager initialization."""
        manager = CrawlerManager()
        
        # Check that crawlers dictionary is initialized with all crawler types
        assert isinstance(manager.crawlers, dict)
        assert len(manager.crawlers) == 6  # All CrawlType enum values
        assert manager.smart_crawler is not None
    
    def test_crawler_types_initialized(self):
        """Test that all crawler types are initialized."""
        manager = CrawlerManager()
        
        # Check that all CrawlType enum values have corresponding crawlers
        for crawl_type in CrawlType:
            assert crawl_type in manager.crawlers
            assert manager.crawlers[crawl_type] is not None
    
    @pytest.mark.asyncio
    async def test_crawl_single_url(self):
        """Test crawling a single URL."""
        manager = CrawlerManager()
        
        # Mock the smart crawler to return a successful result
        with patch.object(manager.smart_crawler, 'crawl') as mock_crawl:
            mock_result = CrawlResult(
                success=True,
                url="https://example.com",
                content="Test content",
                crawl_type=CrawlType.SINGLE_PAGE
            )
            mock_crawl.return_value = mock_result
            
            result = await manager.crawl("https://example.com")
            
            assert result.success is True
            assert result.url == "https://example.com"
            assert result.content == "Test content"
            mock_crawl.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_crawl_error_handling(self):
        """Test error handling during crawl."""
        manager = CrawlerManager()
        
        # Mock the smart crawler to raise an exception
        with patch.object(manager.smart_crawler, 'crawl') as mock_crawl:
            mock_crawl.side_effect = Exception("Crawl failed")
            
            result = await manager.crawl("https://example.com")
            
            assert result.success is False
            assert result.error is not None
            assert "Crawl failed" in result.error.error_message
    
    def test_get_crawler_for_url(self):
        """Test finding the right crawler for a URL."""
        manager = CrawlerManager()
        
        # Test getting crawler for different URL types
        crawler = manager.get_crawler_for_url("https://example.com")
        assert crawler is not None
        
        # Test sitemap URL
        sitemap_crawler = manager.get_crawler_for_url("https://example.com/sitemap.xml")
        assert sitemap_crawler is not None
        
        # Test robots.txt URL
        robots_crawler = manager.get_crawler_for_url("https://example.com/robots.txt")
        assert robots_crawler is not None
    
    def test_get_available_crawlers(self):
        """Test getting list of available crawler types."""
        manager = CrawlerManager()
        
        # Check that all expected crawler types are available
        crawler_types = list(manager.crawlers.keys())
        
        assert CrawlType.SINGLE_PAGE in crawler_types
        assert CrawlType.BATCH in crawler_types
        assert CrawlType.SITEMAP in crawler_types
        assert CrawlType.ROBOTS_TXT in crawler_types
        assert CrawlType.TEXT_FILE in crawler_types
        assert CrawlType.RECURSIVE in crawler_types
        assert len(crawler_types) == 6
