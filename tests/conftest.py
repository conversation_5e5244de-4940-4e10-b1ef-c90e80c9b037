"""Pytest configuration and fixtures for all tests."""

import pytest
import os
from unittest.mock import MagicMock, AsyncMock
from typing import Dict, Any

# Don't set test environment variables globally - let tests control this


@pytest.fixture
def test_env_vars():
    """Fixture to set test environment variables."""
    env_vars = {
        'SUPABASE_URL': 'https://test.supabase.co',
        'SUPABASE_SERVICE_KEY': 'test-service-key',
        'OPENAI_API_KEY': 'test-openai-key',
        'NEO4J_URI': 'bolt://localhost:7687',
        'NEO4J_USER': 'neo4j',
        'NEO4J_PASSWORD': 'test-password',
        'USE_CONTEXTUAL_EMBEDDINGS': 'false',
        'USE_HYBRID_SEARCH': 'false',
        'USE_AGENTIC_RAG': 'false',
        'USE_RERANKING': 'false',
        'USE_KNOWLEDGE_GRAPH': 'false',
        'MCP_SERVER_HOST': '0.0.0.0',
        'MCP_SERVER_PORT': '8051'
    }
    
    # Store original values
    original_env = {}
    for key in env_vars:
        original_env[key] = os.environ.get(key)
    
    # Set test values
    os.environ.update(env_vars)
    
    yield env_vars
    
    # Restore original values
    for key, value in original_env.items():
        if value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = value


@pytest.fixture
def mock_settings(test_env_vars):
    """Create mock settings for testing."""
    from src.config.settings import Settings
    return Settings()


@pytest.fixture
def mock_supabase_client():
    """Create a mock Supabase client."""
    client = MagicMock()
    
    # Mock table operations
    table_mock = MagicMock()
    table_mock.select.return_value = table_mock
    table_mock.insert.return_value = table_mock
    table_mock.update.return_value = table_mock
    table_mock.delete.return_value = table_mock
    table_mock.eq.return_value = table_mock
    table_mock.execute.return_value = MagicMock(data=[])
    
    # Mock RPC operations
    client.rpc.return_value = MagicMock(execute=AsyncMock(return_value=MagicMock(data=[])))
    
    # Make table return the mock
    client.table.return_value = table_mock
    
    return client


@pytest.fixture
def mock_openai_client():
    """Create a mock OpenAI client."""
    client = MagicMock()
    
    # Mock embeddings
    embeddings_mock = MagicMock()
    embeddings_mock.create = AsyncMock(
        return_value=MagicMock(
            data=[MagicMock(embedding=[0.1] * 1536)]
        )
    )
    client.embeddings = embeddings_mock
    
    # Mock chat completions
    chat_mock = MagicMock()
    completion_mock = MagicMock()
    completion_mock.choices = [
        MagicMock(message=MagicMock(content="Mock response"))
    ]
    chat_mock.completions.create = AsyncMock(return_value=completion_mock)
    client.chat = chat_mock
    
    return client


@pytest.fixture
def mock_crawl4ai_response():
    """Create a mock Crawl4AI response."""
    return MagicMock(
        markdown="# Test Content\n\nThis is test content.",
        cleaned_html="<h1>Test Content</h1><p>This is test content.</p>",
        media={"images": []},
        links={"internal": [], "external": []},
        metadata={
            "title": "Test Page",
            "description": "Test description",
            "url": "https://example.com"
        },
        screenshot="",
        success=True,
        error_message=""
    )


@pytest.fixture
def mock_application_context(mock_settings, mock_supabase_client, mock_openai_client):
    """Create a mock application context."""
    from src.core.application_context import ApplicationContext
    
    context = ApplicationContext(mock_settings)
    
    # Override lazy-loaded properties
    context._supabase_client = mock_supabase_client
    context._openai_client = mock_openai_client
    
    # Mock managers
    context._strategy_manager = MagicMock()
    context._strategy_manager.initialize = AsyncMock()
    context._strategy_manager.cleanup = AsyncMock()
    context._strategy_manager.process = AsyncMock(return_value=[])
    
    context._crawler_manager = MagicMock()
    context._crawler_manager.crawl = AsyncMock()
    context._crawler_manager.batch_crawl = AsyncMock()
    
    return context


@pytest.fixture
def sample_rag_results():
    """Create sample RAG results for testing."""
    from src.strategies.base_types import RAGResult
    
    return [
        RAGResult(
            title="Python Tutorial",
            url="https://example.com/python",
            content="Learn Python programming...",
            relevance_score=0.95,
            metadata={"source": "docs"},
            strategies_applied=[]
        ),
        RAGResult(
            title="JavaScript Guide",
            url="https://example.com/js",
            content="JavaScript fundamentals...",
            relevance_score=0.85,
            metadata={"source": "tutorials"},
            strategies_applied=[]
        )
    ]


@pytest.fixture
def sample_crawl_results():
    """Create sample crawl results for testing."""
    from src.crawlers.base_types import CrawlResult
    
    return [
        CrawlResult(
            url="https://example.com/page1",
            content="Content of page 1",
            title="Page 1",
            metadata={"crawled_at": "2024-01-01T00:00:00Z"}
        ),
        CrawlResult(
            url="https://example.com/page2",
            content="Content of page 2",
            title="Page 2",
            metadata={"crawled_at": "2024-01-01T00:01:00Z"}
        )
    ]


# Skip tests that require real external services
def pytest_collection_modifyitems(config, items):
    """Mark tests that require external services."""
    skip_integration = pytest.mark.skip(reason="Requires external services")
    
    for item in items:
        # Skip integration tests if not explicitly enabled
        if "integration" in item.nodeid and not config.getoption("--run-integration", default=False):
            item.add_marker(skip_integration)


def pytest_addoption(parser):
    """Add custom command line options."""
    parser.addoption(
        "--run-integration",
        action="store_true",
        default=False,
        help="Run integration tests that require external services"
    )