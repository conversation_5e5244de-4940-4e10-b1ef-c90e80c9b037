"""Tests for StrategyManager."""

import pytest
from unittest.mock import <PERSON>Mock

from src.strategies.manager import StrategyManager
from src.core.application_context import ApplicationContext


class TestStrategyManager:
    """Test the StrategyManager class."""
    
    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = MagicMock()
        settings.strategies = MagicMock()
        settings.strategies.use_contextual_embeddings = False
        settings.strategies.use_hybrid_search = False
        settings.strategies.use_reranking = False
        settings.strategies.use_agentic_rag = False
        return settings
    
    @pytest.fixture
    def mock_context(self):
        """Create mock application context."""
        context = MagicMock(spec=ApplicationContext)
        return context
    
    def test_initialization(self, mock_settings, mock_context):
        """Test StrategyManager initialization."""
        manager = StrategyManager(mock_settings, mock_context)
        
        assert manager.settings == mock_settings
        assert manager.context == mock_context
        assert manager._strategies == []
        assert manager._strategy_map == {}
    
    def test_get_enabled_strategies_none_enabled(self, mock_settings, mock_context):
        """Test getting enabled strategies when none are enabled."""
        manager = StrategyManager(mock_settings, mock_context)
        
        enabled = manager.get_enabled_strategies()
        assert enabled == []
    
    def test_get_enabled_strategies_some_enabled(self, mock_settings, mock_context):
        """Test getting enabled strategies when some are enabled."""
        mock_settings.strategies.use_contextual_embeddings = True
        mock_settings.strategies.use_hybrid_search = True
        
        manager = StrategyManager(mock_settings, mock_context)
        
        enabled = manager.get_enabled_strategies()
        assert "contextual_embeddings" in enabled
        assert "hybrid_search" in enabled
        assert len(enabled) == 2
    
    def test_get_strategy_metadata(self, mock_settings, mock_context):
        """Test getting strategy metadata."""
        manager = StrategyManager(mock_settings, mock_context)

        metadata = manager.get_strategy_metadata()
        assert isinstance(metadata, dict)
        # Empty dict when no strategies are registered
        assert metadata == {}
    
    def test_get_status_report(self, mock_settings, mock_context):
        """Test getting status report."""
        manager = StrategyManager(mock_settings, mock_context)

        status = manager.get_status_report()
        assert isinstance(status, dict)
        assert "total_strategies" in status
        assert "enabled_strategies" in status
        assert "strategy_metadata" in status
        assert "settings" in status
    
    @pytest.mark.asyncio
    async def test_initialize_all(self, mock_settings, mock_context):
        """Test initializing all strategies."""
        manager = StrategyManager(mock_settings, mock_context)
        
        # Should not raise an exception
        await manager.initialize_all()
    
    @pytest.mark.asyncio
    async def test_close(self, mock_settings, mock_context):
        """Test closing/cleanup of strategies."""
        manager = StrategyManager(mock_settings, mock_context)
        
        # Should not raise an exception
        await manager.close()
    
    def test_get_strategy_none_exists(self, mock_settings, mock_context):
        """Test getting a strategy that doesn't exist."""
        manager = StrategyManager(mock_settings, mock_context)
        
        strategy = manager.get_strategy("nonexistent")
        assert strategy is None
