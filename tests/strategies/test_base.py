"""Unit tests for the base strategy module."""

import pytest
from abc import ABC
from src.strategies.base_types import RAGStrategy, RAGResult


class TestRAGStrategy:
    """Test the RAGStrategy abstract base class."""
    
    def test_is_abstract(self):
        """Test that RAGStrategy cannot be instantiated directly."""
        with pytest.raises(TypeError):
            RAGStrategy()
    
    def test_interface_methods(self):
        """Test that RAGStrategy defines the required interface."""
        # Check that abstract methods are defined
        assert hasattr(RAGStrategy, 'initialize')
        assert hasattr(RAGStrategy, 'process')
        assert hasattr(RAGStrategy, 'cleanup')
        assert hasattr(RAGStrategy, 'get_name')
        assert hasattr(RAGStrategy, 'is_enabled')
        
        # Verify they are abstract
        assert getattr(RAGStrategy.initialize, '__isabstractmethod__', False)
        assert getattr(RAGStrategy.process, '__isabstractmethod__', False)
        assert getattr(RAGStrategy.cleanup, '__isabstractmethod__', <PERSON>alse)
        assert getattr(RAGStrategy.get_name, '__isabstractmethod__', <PERSON>alse)
        assert getattr(RAGStrategy.is_enabled, '__isabstractmethod__', False)
    
    def test_concrete_implementation(self):
        """Test that a concrete implementation can be created."""
        class TestStrategy(RAGStrategy):
            async def initialize(self):
                pass
            
            async def process(self, query, results, context):
                return results
            
            async def cleanup(self):
                pass
            
            def get_name(self):
                return "test"
            
            def is_enabled(self):
                return True
        
        # Should be able to instantiate
        strategy = TestStrategy()
        assert isinstance(strategy, RAGStrategy)
        assert strategy.get_name() == "test"
        assert strategy.is_enabled() is True


class TestRAGResult:
    """Test the RAGResult class."""
    
    def test_initialization(self):
        """Test RAGResult initialization."""
        result = RAGResult(
            title="Test Title",
            url="https://example.com",
            content="Test content",
            relevance_score=0.95
        )
        
        assert result.title == "Test Title"
        assert result.url == "https://example.com"
        assert result.content == "Test content"
        assert result.relevance_score == 0.95
        assert result.metadata == {}
        assert result.strategies_applied == []
    
    def test_initialization_with_metadata(self):
        """Test RAGResult initialization with metadata."""
        metadata = {"key": "value", "count": 42}
        result = RAGResult(
            title="Test",
            url="https://example.com",
            content="Content",
            relevance_score=0.8,
            metadata=metadata
        )
        
        assert result.metadata == metadata
    
    def test_initialization_with_strategies(self):
        """Test RAGResult initialization with strategies applied."""
        strategies = ["strategy1", "strategy2"]
        result = RAGResult(
            title="Test",
            url="https://example.com",
            content="Content",
            relevance_score=0.8,
            strategies_applied=strategies
        )
        
        assert result.strategies_applied == strategies
    
    def test_optional_parameters(self):
        """Test that metadata and strategies_applied have defaults."""
        result = RAGResult(
            title="Test",
            url="https://example.com",
            content="Content",
            relevance_score=0.8
        )
        
        assert isinstance(result.metadata, dict)
        assert len(result.metadata) == 0
        assert isinstance(result.strategies_applied, list)
        assert len(result.strategies_applied) == 0
    
    def test_modification(self):
        """Test that RAGResult fields can be modified."""
        result = RAGResult(
            title="Original",
            url="https://example.com",
            content="Original content",
            relevance_score=0.5
        )
        
        # Modify fields
        result.title = "Modified"
        result.content = "Modified content"
        result.relevance_score = 0.9
        result.metadata["added"] = "value"
        result.strategies_applied.append("new_strategy")
        
        assert result.title == "Modified"
        assert result.content == "Modified content"
        assert result.relevance_score == 0.9
        assert result.metadata["added"] == "value"
        assert "new_strategy" in result.strategies_applied
    
    def test_equality(self):
        """Test RAGResult equality comparison."""
        result1 = RAGResult(
            title="Test",
            url="https://example.com",
            content="Content",
            relevance_score=0.8
        )
        
        result2 = RAGResult(
            title="Test",
            url="https://example.com",
            content="Content",
            relevance_score=0.8
        )
        
        # Should be different objects
        assert result1 is not result2
        
        # But with same content
        assert result1.title == result2.title
        assert result1.url == result2.url
        assert result1.content == result2.content
        assert result1.relevance_score == result2.relevance_score