"""Tests for crawling tools."""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch

from src.tools.crawling_tools import register_crawling_tools
from src.core.application_context import ApplicationContext


class TestCrawlingTools:
    """Test the crawling tools functionality."""
    
    @pytest.fixture
    def mock_server(self):
        """Create mock FastMCP server."""
        server = MagicMock()
        server.tool = MagicMock()
        return server
    
    @pytest.fixture
    def mock_context(self):
        """Create mock application context."""
        context = MagicMock(spec=ApplicationContext)
        mock_crawler_manager = MagicMock()
        context.crawler_manager = mock_crawler_manager
        return context
    
    def test_register_crawling_tools(self, mock_server, mock_context):
        """Test that crawling tools registration doesn't raise errors."""
        # The function should not raise an exception
        register_crawling_tools(mock_server, mock_context)
        
        # The tool decorator should have been called
        assert mock_server.tool.called
    
    def test_mock_context_has_crawler_manager(self, mock_context):
        """Test that mock context has crawler manager."""
        assert hasattr(mock_context, 'crawler_manager')
        assert mock_context.crawler_manager is not None
    
    def test_mock_server_has_tool_method(self, mock_server):
        """Test that mock server has tool method."""
        assert hasattr(mock_server, 'tool')
        assert callable(mock_server.tool)
