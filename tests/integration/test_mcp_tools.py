"""Integration tests for MCP tools with the modular architecture."""

import pytest
import asyncio
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch
import json
import os

from src.config.settings import Settings
from src.core.application_context import ApplicationContext
from src.core.modular_server import create_server
from src.crawlers.base_types import C<PERSON><PERSON><PERSON><PERSON>ult, CrawlError
from src.strategies.base_types import RAGResult


class TestMCPToolsIntegration:
    """Test MCP tools work correctly with the modular system."""
    
    @pytest.fixture
    def mock_settings(self):
        """Create test settings."""
        with patch.dict(os.environ, {
            'SUPABASE_URL': 'https://test.supabase.co',
            'SUPABASE_SERVICE_KEY': 'test-key',
            'OPENAI_API_KEY': 'test-openai-key'
        }):
            return Settings()
    
    @pytest.fixture
    def mock_context(self, mock_settings):
        """Create mock application context."""
        context = ApplicationContext(mock_settings)
        
        # Mock crawler manager
        mock_crawler_manager = MagicMock()
        context._crawler_manager = mock_crawler_manager
        
        # Mock strategy manager
        mock_strategy_manager = MagicMock()
        context._strategy_manager = mock_strategy_manager
        
        # Mock supabase client
        mock_supabase = MagicMock()
        context._supabase_client = mock_supabase
        
        return context
    
    def test_tool_registration(self, mock_settings, mock_context):
        """Test that all tools are registered correctly."""
        server = create_server(mock_settings, mock_context)
        
        # Check that server has tools registered
        # Note: FastMCP stores tools internally
        assert server is not None
        assert server.name == "crawl4ai-mcp"
    
    @pytest.mark.asyncio
    async def test_crawl_single_page_tool(self, mock_context):
        """Test crawl_single_page tool integration."""
        from src.tools.crawling_tools import register_crawling_tools
        from mcp.server.fastmcp import FastMCP
        
        # Create a test server
        server = FastMCP("test-server")
        
        # Mock crawler response
        mock_result = CrawlResult(
            url="https://example.com",
            content="Test page content",
            title="Test Page",
            metadata={"status": "success"}
        )
        mock_context.crawler_manager.crawl = AsyncMock(return_value=mock_result)
        
        # Register tools
        register_crawling_tools(server, mock_context)
        
        # Get the registered tool handler
        # Note: This is a simplified test - in reality we'd need to invoke through MCP
        tool_handler = None
        for tool_name, handler in server._tools.items():
            if tool_name == "crawl_single_page":
                tool_handler = handler
                break
        
        assert tool_handler is not None
        
        # Test tool execution
        result = await tool_handler(
            url="https://example.com",
            include_images=True,
            max_depth=2
        )
        
        # Verify result
        assert result["success"] is True
        assert result["content"] == "Test page content"
        assert result["title"] == "Test Page"
        assert result["url"] == "https://example.com"
        
        # Verify crawler was called with options
        mock_context.crawler_manager.crawl.assert_called_once_with(
            "https://example.com",
            {"include_images": True, "max_depth": 2}
        )
    
    @pytest.mark.asyncio
    async def test_smart_crawl_url_tool(self, mock_context):
        """Test smart_crawl_url tool with batch processing."""
        from src.tools.crawling_tools import register_crawling_tools
        from mcp.server.fastmcp import FastMCP
        
        server = FastMCP("test-server")
        
        # Mock batch crawl response
        mock_results = [
            CrawlResult(url="https://example.com/1", content="Content 1"),
            CrawlResult(url="https://example.com/2", content="Content 2")
        ]
        mock_context.crawler_manager.batch_crawl = AsyncMock(return_value=mock_results)
        
        # Register tools
        register_crawling_tools(server, mock_context)
        
        # Get tool handler
        tool_handler = None
        for tool_name, handler in server._tools.items():
            if tool_name == "smart_crawl_url":
                tool_handler = handler
                break
        
        # Test with multiple URLs
        result = await tool_handler(
            url=["https://example.com/1", "https://example.com/2"]
        )
        
        # Verify batch processing
        assert result["success"] is True
        assert result["results_count"] == 2
        assert len(result["results"]) == 2
        assert result["results"][0]["content"] == "Content 1"
        assert result["results"][1]["content"] == "Content 2"
    
    @pytest.mark.asyncio
    async def test_perform_rag_query_tool(self, mock_context):
        """Test perform_rag_query tool integration."""
        from src.tools.rag_tools import register_rag_tools
        from mcp.server.fastmcp import FastMCP
        
        server = FastMCP("test-server")
        
        # Mock Supabase search response
        mock_search_results = {
            "data": [
                {
                    "page_url": "https://example.com/1",
                    "page_title": "Python Guide",
                    "content": "Python programming basics",
                    "similarity": 0.95,
                    "source_name": "example.com"
                }
            ]
        }
        mock_context.supabase_client.rpc.return_value.execute.return_value = mock_search_results
        
        # Mock strategy processing
        mock_rag_results = [
            RAGResult(
                title="Python Guide",
                url="https://example.com/1",
                content="Enhanced Python programming basics",
                relevance_score=0.95,
                strategies_applied=["contextual_embeddings"]
            )
        ]
        mock_context.strategy_manager.process = AsyncMock(return_value=mock_rag_results)
        
        # Register tools
        register_rag_tools(server, mock_context)
        
        # Get tool handler
        tool_handler = None
        for tool_name, handler in server._tools.items():
            if tool_name == "perform_rag_query":
                tool_handler = handler
                break
        
        # Test RAG query
        result_json = await tool_handler(
            query="Python programming",
            source_filter="example.com",
            num_results=5
        )
        
        result = json.loads(result_json)
        
        # Verify result
        assert result["success"] is True
        assert result["query"] == "Python programming"
        assert len(result["results"]) == 1
        assert result["results"][0]["title"] == "Python Guide"
        assert "contextual_embeddings" in result["results"][0]["strategies_applied"]
        
        # Verify strategy manager was called
        mock_context.strategy_manager.process.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_tool_error_handling(self, mock_context):
        """Test that tools handle errors gracefully."""
        from src.tools.crawling_tools import register_crawling_tools
        from mcp.server.fastmcp import FastMCP
        
        server = FastMCP("test-server")
        
        # Mock crawler error
        mock_error_result = CrawlResult(
            url="https://invalid.com",
            content="",
            error=CrawlError("DNS resolution failed", "DNS_ERROR")
        )
        mock_context.crawler_manager.crawl = AsyncMock(return_value=mock_error_result)
        
        register_crawling_tools(server, mock_context)
        
        # Get tool handler
        tool_handler = None
        for tool_name, handler in server._tools.items():
            if tool_name == "crawl_single_page":
                tool_handler = handler
                break
        
        # Test error handling
        result = await tool_handler(url="https://invalid.com")
        
        # Should return error response
        assert result["success"] is False
        assert result["error"] == "DNS resolution failed"
        assert result["error_type"] == "DNS_ERROR"
    
    @pytest.mark.asyncio
    async def test_knowledge_graph_tools(self, mock_context):
        """Test knowledge graph tool integration."""
        from src.tools.knowledge_graph_tools import register_knowledge_graph_tools
        from mcp.server.fastmcp import FastMCP
        
        server = FastMCP("test-server")
        
        # Enable knowledge graph
        mock_context.settings.strategies.use_knowledge_graph = True
        
        # Mock Neo4j response
        mock_kg_results = [
            {
                "entity": "Python",
                "type": "Language",
                "relationships": ["used_for", "created_by"]
            }
        ]
        
        # Note: In real implementation, would need to mock Neo4j client
        # This is a simplified test
        
        register_knowledge_graph_tools(server, mock_context)
        
        # Verify tools are registered
        tool_names = list(server._tools.keys())
        kg_tools = [name for name in tool_names if "knowledge_graph" in name]
        
        # Should have knowledge graph tools when enabled
        assert len(kg_tools) > 0


class TestEndToEndWorkflow:
    """Test complete end-to-end workflows."""
    
    @pytest.mark.asyncio
    async def test_crawl_store_query_workflow(self):
        """Test complete workflow: crawl -> store -> query."""
        with patch.dict(os.environ, {
            'SUPABASE_URL': 'https://test.supabase.co',
            'SUPABASE_SERVICE_KEY': 'test-key',
            'OPENAI_API_KEY': 'test-openai-key',
            'USE_CONTEXTUAL_EMBEDDINGS': 'true'
        }):
            settings = Settings()
            context = ApplicationContext(settings)
            
            # Mock components
            mock_crawler_manager = MagicMock()
            mock_strategy_manager = MagicMock()
            mock_supabase = MagicMock()
            
            context._crawler_manager = mock_crawler_manager
            context._strategy_manager = mock_strategy_manager
            context._supabase_client = mock_supabase
            
            # Step 1: Crawl a page
            crawl_result = CrawlResult(
                url="https://docs.example.com/python",
                content="Python is a high-level programming language.",
                title="Python Documentation"
            )
            mock_crawler_manager.crawl = AsyncMock(return_value=crawl_result)
            
            # Step 2: Store in database (mocked)
            mock_supabase.table.return_value.insert.return_value.execute.return_value = {
                "data": [{"id": "123"}]
            }
            
            # Step 3: Query with RAG
            mock_search_results = {
                "data": [{
                    "page_url": "https://docs.example.com/python",
                    "page_title": "Python Documentation",
                    "content": "Python is a high-level programming language.",
                    "similarity": 0.98
                }]
            }
            mock_supabase.rpc.return_value.execute.return_value = mock_search_results
            
            # Step 4: Process with strategies
            enhanced_results = [
                RAGResult(
                    title="Python Documentation",
                    url="https://docs.example.com/python",
                    content="Python is a high-level, interpreted programming language known for its simplicity.",
                    relevance_score=0.98,
                    strategies_applied=["contextual_embeddings"]
                )
            ]
            mock_strategy_manager.process = AsyncMock(return_value=enhanced_results)
            
            # Execute workflow
            await context.initialize()
            
            # Crawl
            crawled = await context.crawler_manager.crawl("https://docs.example.com/python")
            assert crawled.content == "Python is a high-level programming language."
            
            # Query
            query_results = await context.strategy_manager.process(
                "What is Python?",
                [RAGResult(
                    title=crawled.title,
                    url=crawled.url,
                    content=crawled.content,
                    relevance_score=0.98
                )]
            )
            
            assert len(query_results) == 1
            assert "interpreted" in query_results[0].content
            assert query_results[0].relevance_score == 0.98
            
            await context.cleanup()
    
    @pytest.mark.asyncio
    async def test_concurrent_workflow(self):
        """Test system handles concurrent workflows correctly."""
        settings = Settings()
        
        # Create multiple contexts for concurrent operations
        contexts = [ApplicationContext(settings) for _ in range(3)]
        
        # Mock all contexts
        for i, context in enumerate(contexts):
            mock_crawler = MagicMock()
            mock_crawler.crawl = AsyncMock(
                return_value=CrawlResult(
                    url=f"https://example.com/{i}",
                    content=f"Content {i}"
                )
            )
            context._crawler_manager = mock_crawler
        
        # Run concurrent operations
        async def process_context(ctx, idx):
            await ctx.initialize()
            result = await ctx.crawler_manager.crawl(f"https://example.com/{idx}")
            await ctx.cleanup()
            return result
        
        # Execute concurrently
        results = await asyncio.gather(*[
            process_context(ctx, i) for i, ctx in enumerate(contexts)
        ])
        
        # Verify all completed successfully
        assert len(results) == 3
        for i, result in enumerate(results):
            assert result.content == f"Content {i}"