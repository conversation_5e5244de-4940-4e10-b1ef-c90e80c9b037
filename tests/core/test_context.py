"""Unit tests for the application context module."""

import pytest
from unittest.mock import <PERSON><PERSON>ock, patch, AsyncMock
from src.core.application_context import ApplicationContext
from src.config.settings import Settings


class TestApplicationContext:
    """Test the ApplicationContext class."""
    
    @pytest.fixture
    def mock_settings(self):
        """Create mock settings for testing."""
        settings = MagicMock(spec=Settings)

        # Mock database settings
        settings.database = MagicMock()
        settings.database.url = "https://test.supabase.co"
        settings.database.service_key = MagicMock()
        settings.database.service_key.get_secret_value.return_value = "test-key"

        # Mock AI settings
        settings.ai = MagicMock()
        settings.ai.openai_api_key = MagicMock()
        settings.ai.openai_api_key.get_secret_value.return_value = "test-openai"

        # Mock strategy settings
        settings.strategies = MagicMock()
        settings.strategies.use_contextual_embeddings = False
        settings.strategies.use_hybrid_search = False
        settings.strategies.use_reranking = False
        settings.strategies.use_agentic_rag = False
        settings.strategies.use_knowledge_graph = False

        return settings
    
    def test_initialization(self, mock_settings):
        """Test ApplicationContext initialization."""
        context = ApplicationContext(mock_settings)
        
        assert context.settings == mock_settings
        assert context._supabase_client is None
        assert context._openai_client is None
        assert context._strategy_manager is None
        assert context._crawler_manager is None
        assert context._script_analyzer is None
    
    @patch('src.core.application_context.create_client')
    def test_supabase_client_lazy_loading(self, mock_create_client, mock_settings):
        """Test lazy loading of Supabase client."""
        mock_client = MagicMock()
        mock_create_client.return_value = mock_client
        
        context = ApplicationContext(mock_settings)
        
        # First access should create client
        client1 = context.supabase_client
        assert client1 == mock_client
        mock_create_client.assert_called_once_with(
            "https://test.supabase.co",
            "test-key"
        )
        
        # Second access should return cached client
        client2 = context.supabase_client
        assert client2 == client1
        assert mock_create_client.call_count == 1
    
    @patch('src.core.application_context.OpenAI')
    def test_openai_client_lazy_loading(self, mock_openai_class, mock_settings):
        """Test lazy loading of OpenAI client."""
        mock_client = MagicMock()
        mock_openai_class.return_value = mock_client
        
        context = ApplicationContext(mock_settings)
        
        # First access should create client
        client1 = context.openai_client
        assert client1 == mock_client
        mock_openai_class.assert_called_once_with(api_key="test-openai")
        
        # Second access should return cached client
        client2 = context.openai_client
        assert client2 == client1
        assert mock_openai_class.call_count == 1
    
    def test_openai_client_none_when_no_api_key(self, mock_settings):
        """Test OpenAI client returns None when no API key."""
        mock_settings.ai.openai_api_key = None
        
        context = ApplicationContext(mock_settings)
        assert context.openai_client is None
    
    @patch('src.strategies.manager.StrategyManager')
    def test_strategy_manager_lazy_loading(self, mock_manager_class, mock_settings):
        """Test lazy loading of strategy manager."""
        mock_manager = MagicMock()
        mock_manager_class.return_value = mock_manager
        
        context = ApplicationContext(mock_settings)
        
        # First access should create manager
        manager1 = context.strategy_manager
        assert manager1 == mock_manager
        mock_manager_class.assert_called_once_with(context)
        
        # Second access should return cached manager
        manager2 = context.strategy_manager
        assert manager2 == manager1
        assert mock_manager_class.call_count == 1
    
    @patch('src.crawlers.manager.CrawlerManager')
    def test_crawler_manager_lazy_loading(self, mock_manager_class, mock_settings):
        """Test lazy loading of crawler manager."""
        mock_manager = MagicMock()
        mock_manager_class.return_value = mock_manager
        
        context = ApplicationContext(mock_settings)
        
        # First access should create manager
        manager1 = context.crawler_manager
        assert manager1 == mock_manager
        mock_manager_class.assert_called_once()
        
        # Second access should return cached manager
        manager2 = context.crawler_manager
        assert manager2 == manager1
        assert mock_manager_class.call_count == 1
    
    @patch('knowledge_graphs.ai_script_analyzer.AIScriptAnalyzer')
    def test_script_analyzer_lazy_loading(self, mock_analyzer_class, mock_settings):
        """Test lazy loading of script analyzer."""
        mock_analyzer = MagicMock()
        mock_analyzer_class.return_value = mock_analyzer
        
        context = ApplicationContext(mock_settings)
        
        # First access should create analyzer
        analyzer1 = context.script_analyzer
        assert analyzer1 == mock_analyzer
        mock_analyzer_class.assert_called_once()
        
        # Second access should return cached analyzer
        analyzer2 = context.script_analyzer
        assert analyzer2 == analyzer1
        assert mock_analyzer_class.call_count == 1
    
    @pytest.mark.asyncio
    async def test_initialize(self, mock_settings):
        """Test async initialization."""
        context = ApplicationContext(mock_settings)
        
        # Mock the strategy manager
        mock_strategy_manager = MagicMock()
        mock_strategy_manager.initialize = AsyncMock()
        context._strategy_manager = mock_strategy_manager
        
        await context.initialize()
        
        mock_strategy_manager.initialize.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cleanup_with_resources(self, mock_settings):
        """Test cleanup when resources exist."""
        context = ApplicationContext(mock_settings)
        
        # Create mock resources
        mock_supabase = MagicMock()
        mock_strategy_manager = MagicMock()
        mock_strategy_manager.cleanup = AsyncMock()
        
        context._supabase_client = mock_supabase
        context._strategy_manager = mock_strategy_manager
        
        await context.cleanup()
        
        mock_strategy_manager.cleanup.assert_called_once()
        # Note: Supabase client doesn't have explicit cleanup
    
    @pytest.mark.asyncio
    async def test_cleanup_without_resources(self, mock_settings):
        """Test cleanup when no resources exist."""
        context = ApplicationContext(mock_settings)
        
        # Should not raise any errors
        await context.cleanup()
        
        assert context._supabase_client is None
        assert context._strategy_manager is None
    
    @pytest.mark.asyncio
    async def test_context_manager(self, mock_settings):
        """Test using ApplicationContext as async context manager."""
        context = ApplicationContext(mock_settings)
        
        # Mock the strategy manager
        mock_strategy_manager = MagicMock()
        mock_strategy_manager.initialize = AsyncMock()
        mock_strategy_manager.cleanup = AsyncMock()
        context._strategy_manager = mock_strategy_manager
        
        async with context as ctx:
            assert ctx == context
            mock_strategy_manager.initialize.assert_called_once()
        
        mock_strategy_manager.cleanup.assert_called_once()
    
    def test_get_supabase_client_error_handling(self, mock_settings):
        """Test error handling in Supabase client creation."""
        mock_settings.database.url = None
        mock_settings.database.service_key = None
        
        context = ApplicationContext(mock_settings)
        
        # Should raise ValueError when credentials missing
        with pytest.raises(ValueError, match="Supabase credentials not configured"):
            _ = context.supabase_client