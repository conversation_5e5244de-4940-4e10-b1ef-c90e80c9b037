"""Tests for server creation functionality."""

import pytest
from unittest.mock import MagicMock, patch

from src.core.server import create_mcp_server, create_and_configure_server, register_all_tools


class TestServerCreation:
    """Test the server creation functionality."""
    
    def test_create_mcp_server_default(self):
        """Test creating MCP server with default parameters."""
        server = create_mcp_server()

        assert server is not None
        # FastMCP server should be created successfully
        assert hasattr(server, 'name')
    
    def test_create_mcp_server_custom_params(self):
        """Test creating MCP server with custom parameters."""
        server = create_mcp_server(
            name="custom-server",
            description="Custom description",
            host="127.0.0.1",
            port=9000
        )

        assert server is not None
        # FastMCP server should be created successfully
        assert hasattr(server, 'name')
    
    @patch('src.core.server.register_all_tools')
    def test_create_and_configure_server_basic(self, mock_register_tools):
        """Test creating and configuring server."""
        server = create_and_configure_server()
        
        assert server is not None
        # Should register tools by default
        mock_register_tools.assert_called_once()
    
    @patch('src.core.server.register_all_tools')
    def test_create_and_configure_server_no_tools(self, mock_register_tools):
        """Test creating server without registering tools."""
        server = create_and_configure_server(register_tools=False)
        
        assert server is not None
        # Should not register tools
        mock_register_tools.assert_not_called()
    
    @patch('src.core.server.register_all_tools')
    def test_create_and_configure_server_with_context(self, mock_register_tools):
        """Test creating server with custom context."""
        mock_context = MagicMock()
        
        server = create_and_configure_server(context=mock_context)
        
        assert server is not None
        # Should register tools with the provided context
        mock_register_tools.assert_called_once()
        args, kwargs = mock_register_tools.call_args
        assert len(args) >= 2  # server and context
    
    def test_register_all_tools_basic(self):
        """Test registering tools with server."""
        mock_server = MagicMock()
        
        # Should not raise an exception
        register_all_tools(mock_server)
    
    def test_register_all_tools_with_context(self):
        """Test registering tools with server and context."""
        mock_server = MagicMock()
        mock_context = MagicMock()
        
        # Should not raise an exception
        register_all_tools(mock_server, mock_context)
