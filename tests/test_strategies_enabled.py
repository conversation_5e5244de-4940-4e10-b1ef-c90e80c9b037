#!/usr/bin/env python3
"""Test script for strategies with some enabled via mock configuration."""

import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config import Settings
from core.context import Crawl4AIContext, crawl4ai_lifespan
from core.server import create_mcp_server
from strategies.manager import StrategyManager


async def test_enabled_strategies():
    """Test strategies with some enabled."""
    print("=== Testing Enabled Strategies ===\n")
    
    # Create test configuration with some strategies enabled
    test_config = {
        "SUPABASE_URL": "https://test.supabase.co",
        "SUPABASE_SERVICE_KEY": "test-key",
        "OPENAI_API_KEY": "test-openai-key",
        "USE_CONTEXTUAL_EMBEDDINGS": "true",
        "USE_AGENTIC_RAG": "true",
        "USE_HYBRID_SEARCH": "false",
        "USE_RERANKING": "false"
    }
    
    # Create settings with test configuration
    test_settings = Settings.from_dict(test_config)
    
    print("1. Testing with enabled strategies...")
    print(f"   Contextual embeddings: {test_settings.strategies.use_contextual_embeddings}")
    print(f"   Agentic RAG: {test_settings.strategies.use_agentic_rag}")
    print(f"   Hybrid search: {test_settings.strategies.use_hybrid_search}")
    print(f"   Reranking: {test_settings.strategies.use_reranking}")
    
    # Create a mock context with test settings
    server = create_mcp_server()
    
    async with crawl4ai_lifespan(server) as ctx:
        # Override the settings in context
        ctx.settings = test_settings
        
        # Create strategy manager with test settings
        strategy_manager = StrategyManager(test_settings, ctx)
        
        enabled_strategies = strategy_manager.get_enabled_strategies()
        print(f"\n✅ Enabled strategies: {enabled_strategies}")
        
        # Test with mock data
        mock_data = [
            {
                'content': 'This is a Python tutorial with examples.',
                'metadata': {'title': 'Python Tutorial', 'url': 'https://example.com/tutorial'}
            },
            {
                'content': '''```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
```
This function calculates Fibonacci numbers recursively.''',
                'metadata': {'title': 'Fibonacci Example', 'url': 'https://example.com/fib'}
            }
        ]
        
        # Test crawl processing
        print("\n2. Testing crawl result processing...")
        processed = await strategy_manager.process_crawl_results(
            mock_data,
            source_url='https://example.com',
            source_id='test_source'
        )
        
        print(f"✅ Processed {len(processed)} results")
        
        # Check for enhancements
        for i, result in enumerate(processed):
            metadata = result.get('metadata', {})
            enhancements = []
            
            if metadata.get('contextual_enhancement'):
                enhancements.append('contextual_embeddings')
            if metadata.get('agentic_rag_processed'):
                enhancements.append('agentic_rag')
            if metadata.get('has_code'):
                enhancements.append(f"code_extraction ({metadata.get('code_count', 0)} examples)")
            
            if enhancements:
                print(f"   Result {i+1}: {', '.join(enhancements)}")
            
            # Show content changes for contextual embeddings
            if 'original_content' in result:
                original_len = len(result['original_content'])
                enhanced_len = len(result['content'])
                print(f"   Content enhanced: {original_len} → {enhanced_len} chars")
        
        # Test search enhancement
        print("\n3. Testing search enhancement...")
        mock_search_results = [
            {
                'id': 'doc1',
                'content': 'Python programming guide with examples',
                'similarity': 0.85,
                'metadata': {}
            }
        ]
        
        enhanced = await strategy_manager.enhance_search(
            query='Python programming tutorial',
            results=mock_search_results
        )
        
        print(f"✅ Enhanced {len(enhanced)} search results")
        
        # Get strategy metadata
        print("\n4. Strategy metadata:")
        metadata = strategy_manager.get_strategy_metadata()
        for name, meta in metadata.items():
            print(f"   {name}: {meta.get('description', 'No description')}")
        
        await strategy_manager.close()


if __name__ == "__main__":
    asyncio.run(test_enabled_strategies())