#!/usr/bin/env python3
"""Test script to verify the configuration system."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import load_settings, Settings

def test_configuration():
    """Test the configuration loading and validation."""
    print("=== Testing Configuration System ===\n")
    
    # Test 1: Load configuration
    print("1. Loading configuration from environment...")
    try:
        settings = load_settings()
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return
    
    # Test 2: Check loaded values
    print("\n2. Checking loaded values...")
    
    # Database settings
    if settings.database:
        print(f"✅ Database URL: {settings.database.url[:30]}..." if settings.database.url else "❌ No database URL")
        print(f"✅ Database key: {'***' if settings.database.key.get_secret_value() else '❌ No key'}")
    else:
        print("⚠️  No database configuration")
    
    # Neo4j settings
    if settings.neo4j:
        print(f"✅ Neo4j URI: {settings.neo4j.uri}")
        print(f"✅ Neo4j user: {settings.neo4j.user}")
        print(f"✅ Neo4j password: {'***' if settings.neo4j.password else 'Not set'}")
    else:
        print("⚠️  No Neo4j configuration")
    
    # AI settings
    if settings.ai:
        print(f"✅ OpenAI API key: {'***' if settings.ai.openai_api_key.get_secret_value() else '❌ No key'}")
        print(f"✅ Embedding model: {settings.ai.embedding_model}")
        print(f"✅ Model choice: {settings.ai.model_choice or 'Not specified'}")
    else:
        print("⚠️  No AI configuration")
    
    # Strategy settings
    print(f"\n✅ Strategies enabled:")
    print(f"   - Contextual embeddings: {settings.strategies.use_contextual_embeddings}")
    print(f"   - Hybrid search: {settings.strategies.use_hybrid_search}")
    print(f"   - Reranking: {settings.strategies.use_reranking}")
    print(f"   - Agentic RAG: {settings.strategies.use_agentic_rag}")
    print(f"   - Knowledge graph: {settings.strategies.use_knowledge_graph}")
    
    # Server settings
    print(f"\n✅ Server configuration:")
    print(f"   - Host: {settings.server.host}")
    print(f"   - Port: {settings.server.port}")
    
    # Test 3: Validation
    print("\n3. Running validation...")
    validation_result = settings.validate()
    
    print(f"✅ Validation status: {validation_result.health.value}")
    print(f"✅ Is valid: {validation_result.is_valid}")
    
    if validation_result.errors:
        print(f"❌ Errors:")
        for error in validation_result.errors:
            print(f"   - {error}")
    
    if validation_result.warnings:
        print(f"⚠️  Warnings:")
        for warning in validation_result.warnings:
            print(f"   - {warning}")
    
    # Test 4: Health report
    print("\n4. Getting health report...")
    health_report = settings.get_health_report()
    print(f"✅ Health: {health_report['health']}")
    print(f"✅ Fallbacks active: {health_report['fallbacks_active']}")
    
    # Test 5: Access methods
    print("\n5. Testing access methods...")
    
    # Using get method with dot notation
    host = settings.get("server.host")
    print(f"✅ Dot notation access (server.host): {host}")
    
    # Direct attribute access
    port = settings.server.port
    print(f"✅ Direct access (server.port): {port}")
    
    # Test 6: Create from dict
    print("\n6. Testing Settings.from_dict...")
    test_config = {
        "SUPABASE_URL": "https://test.supabase.co",
        "SUPABASE_SERVICE_KEY": "test-key",
        "OPENAI_API_KEY": "test-openai-key",
        "USE_RERANKING": "true",
        "USE_KNOWLEDGE_GRAPH": "false",
        "HOST": "localhost",
        "PORT": "8080"
    }
    
    test_settings = Settings.from_dict(test_config)
    print(f"✅ Test settings created")
    print(f"   - Database URL: {test_settings.database.url}")
    print(f"   - Reranking enabled: {test_settings.strategies.use_reranking}")
    print(f"   - Server port: {test_settings.server.port}")
    
    print("\n=== Configuration Test Complete ===")

if __name__ == "__main__":
    test_configuration()