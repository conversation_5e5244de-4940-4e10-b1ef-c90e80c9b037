#!/usr/bin/env python3
"""Test script to verify Phase 2 refactoring works correctly."""

import sys
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import with full paths since we're running as a script
import src.core as core
from src.core.context import Crawl4AIContext, crawl4ai_lifespan
from src.core.server import create_mcp_server
from src.config import load_settings
from mcp.server.fastmcp import FastMCP


async def test_core_modules():
    """Test the core module functionality."""
    print("=== Testing Phase 2: Core Infrastructure ===\n")
    
    # Test 1: Settings loading
    print("1. Testing configuration loading...")
    try:
        settings = load_settings()
        print("✅ Configuration loaded successfully")
        print(f"   Server: {settings.server.host}:{settings.server.port}")
        print(f"   Reranking enabled: {settings.strategies.use_reranking}")
        print(f"   Knowledge graph enabled: {settings.strategies.use_knowledge_graph}")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        return
    
    # Test 2: Server creation
    print("\n2. Testing MCP server creation...")
    try:
        server = create_mcp_server(
            name="test-server",
            description="Test server for Phase 2"
        )
        print("✅ MCP server created successfully")
        print(f"   Server name: {server.name}")
    except Exception as e:
        print(f"❌ Failed to create server: {e}")
        return
    
    # Test 3: Context lifecycle
    print("\n3. Testing context lifecycle...")
    try:
        async with crawl4ai_lifespan(server) as ctx:
            print("✅ Context created successfully")
            
            # Verify context attributes
            assert isinstance(ctx, Crawl4AIContext)
            assert ctx.crawler is not None
            # Supabase client might be None if not configured
            # assert ctx.supabase_client is not None
            assert ctx.settings is not None
            print("✅ Core context attributes present")
            
            # Test lazy loading of strategy manager
            print("\n4. Testing lazy loading of strategy manager...")
            strategy_manager = ctx.get_strategy_manager()
            print("✅ Strategy manager loaded successfully")
            
            # Check optional components based on settings
            if settings.strategies.use_reranking:
                if ctx.reranking_model:
                    print("✅ Reranking model loaded")
                else:
                    print("⚠️  Reranking enabled but model not loaded")
            
            if settings.strategies.use_knowledge_graph:
                if ctx.knowledge_validator or ctx.repo_extractor:
                    print("✅ Knowledge graph components loaded")
                else:
                    print("⚠️  Knowledge graph enabled but components not loaded")
        
        print("\n✅ Context cleaned up successfully")
        
    except Exception as e:
        print(f"❌ Context lifecycle test failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Test 4: Server independence
    print("\n5. Testing multiple server instances...")
    try:
        server1 = create_mcp_server(name="server1", port=8051)
        server2 = create_mcp_server(name="server2", port=8052)
        
        assert server1.name == "server1"
        assert server2.name == "server2"
        print("✅ Multiple servers can be created independently")
        
    except Exception as e:
        print(f"❌ Multiple server test failed: {e}")
    
    print("\n=== Phase 2 Testing Complete ===")
    print("\nSummary:")
    print("- Core modules created successfully")
    print("- Configuration integrated")
    print("- Context lifecycle working")
    print("- Dependency injection pattern implemented")
    print("- Ready for Phase 3: Strategy Pattern")


async def test_performance():
    """Test performance metrics for Phase 2."""
    print("\n=== Performance Testing ===")
    
    import time
    
    # Measure server creation time
    start = time.time()
    server = create_mcp_server()
    server_time = time.time() - start
    print(f"Server creation time: {server_time:.3f}s")
    
    # Measure context initialization time
    start = time.time()
    async with crawl4ai_lifespan(server) as ctx:
        init_time = time.time() - start
        print(f"Context initialization time: {init_time:.3f}s")
    
    # Check against success criteria
    total_time = server_time + init_time
    print(f"\nTotal startup time: {total_time:.3f}s")
    if total_time < 5:
        print("✅ Meets performance criteria (<5s startup)")
    else:
        print("❌ Exceeds performance criteria (>5s startup)")


if __name__ == "__main__":
    # Run tests
    asyncio.run(test_core_modules())
    asyncio.run(test_performance())