"""Unit tests for the configuration settings module."""

import os
import pytest
from unittest.mock import patch, MagicMock
from src.config.settings import Settings, StrategySettings, DatabaseSettings, AISettings, Neo4jSettings, ServerSettings


class TestSettings:
    """Test the main Settings class."""
    
    def test_default_settings(self):
        """Test Settings with default values."""
        settings = Settings()
        
        # Check defaults
        assert hasattr(settings, 'server')
        assert settings.server.host == "0.0.0.0"
        assert settings.server.port == 8051
        assert hasattr(settings, 'strategies')
        assert settings.strategies.use_contextual_embeddings is False
        assert settings.strategies.use_hybrid_search is False
        assert settings.strategies.use_reranking is False
        assert settings.strategies.use_agentic_rag is False
        
    def test_env_var_loading(self):
        """Test Settings loading from environment variables."""
        with patch.dict(os.environ, {
            'USE_KNOWLEDGE_GRAPH': 'true',
            'HOST': 'localhost',
            'PORT': '9000',
            'USE_CONTEXTUAL_EMBEDDINGS': 'true',
            'USE_HYBRID_SEARCH': 'true',
            'SUPABASE_URL': 'https://test.supabase.co',
            'SUPABASE_SERVICE_KEY': 'test-key',
            'OPENAI_API_KEY': 'test-openai-key'
        }):
            settings = Settings()
            
            assert settings.strategies.use_knowledge_graph is True
            assert settings.server.host == "localhost"
            assert settings.server.port == 9000
            assert settings.strategies.use_contextual_embeddings is True
            assert settings.strategies.use_hybrid_search is True
            assert settings.database.url == "https://test.supabase.co"
            assert settings.database.key.get_secret_value() == "test-key"
            assert settings.ai.openai_api_key.get_secret_value() == "test-openai-key"
    
    def test_validate_method(self):
        """Test the validate method returns correct status."""
        # Test with missing required settings
        settings = Settings()
        result = settings.validate()
        
        assert result.is_valid is False
        assert len(result.errors) > 0
        
        # Test with all required settings
        with patch.dict(os.environ, {
            'SUPABASE_URL': 'https://test.supabase.co',
            'SUPABASE_SERVICE_KEY': 'test-key',
            'OPENAI_API_KEY': 'test-key'
        }):
            settings = Settings()
            result = settings.validate()
            
            # Should be valid with required settings
            assert result.is_valid is True
    
    def test_get_health_report(self):
        """Test health status reporting."""
        with patch.dict(os.environ, {
            'SUPABASE_URL': 'https://test.supabase.co',
            'SUPABASE_SERVICE_KEY': 'test-key',
            'OPENAI_API_KEY': 'test-key',
            'USE_CONTEXTUAL_EMBEDDINGS': 'true',
            'USE_KNOWLEDGE_GRAPH': 'true',
            'NEO4J_URI': 'bolt://localhost:7687',
            'NEO4J_USER': 'neo4j',
            'NEO4J_PASSWORD': 'password'
        }):
            settings = Settings()
            health = settings.get_health_report()
            
            assert 'health' in health
            assert 'is_valid' in health
            assert health['is_valid'] is True


class TestStrategySettings:
    """Test the StrategySettings class."""
    
    def test_default_values(self):
        """Test default strategy settings."""
        settings = StrategySettings()
        
        assert settings.use_contextual_embeddings is False
        assert settings.use_hybrid_search is False
        assert settings.use_reranking is False
        assert settings.use_agentic_rag is False
        
    def test_env_var_parsing(self):
        """Test parsing of strategy environment variables."""
        with patch.dict(os.environ, {
            'USE_CONTEXTUAL_EMBEDDINGS': 'true',
            'USE_HYBRID_SEARCH': 'TRUE',
            'USE_RERANKING': '1',
            'USE_AGENTIC_RAG': 'yes'
        }):
            settings = StrategySettings()
            
            assert settings.use_contextual_embeddings is True
            assert settings.use_hybrid_search is True
            assert settings.use_reranking is True
            assert settings.use_agentic_rag is True
    
    def test_false_values(self):
        """Test parsing of false values."""
        with patch.dict(os.environ, {
            'USE_CONTEXTUAL_EMBEDDINGS': 'false',
            'USE_HYBRID_SEARCH': '0',
            'USE_RERANKING': 'no',
            'USE_AGENTIC_RAG': ''
        }):
            settings = StrategySettings()
            
            assert settings.use_contextual_embeddings is False
            assert settings.use_hybrid_search is False
            assert settings.use_reranking is False
            assert settings.use_agentic_rag is False


class TestDatabaseSettings:
    """Test the DatabaseSettings class."""
    
    def test_required_fields(self):
        """Test that database settings are required."""
        # DatabaseSettings requires url and key
        with pytest.raises(ValueError):
            DatabaseSettings()
        
    def test_valid_initialization(self):
        """Test valid initialization."""
        settings = DatabaseSettings(
            url='https://test.supabase.co',
            key='sk-test-key'
        )
        
        assert settings.url == 'https://test.supabase.co'
        assert settings.key.get_secret_value() == 'sk-test-key'


class TestAISettings:
    """Test the AISettings class."""
    
    def test_required_api_key(self):
        """Test that OpenAI API key is required."""
        # AISettings requires openai_api_key in this implementation
        with pytest.raises(ValueError):
            AISettings()
        
    def test_env_var_loading(self):
        """Test loading from environment variables."""
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'sk-test-openai'}):
            settings = AISettings(openai_api_key='sk-test-openai')
            assert settings.openai_api_key.get_secret_value() == 'sk-test-openai'


class TestNeo4jSettings:
    """Test the Neo4jSettings class."""
    
    def test_optional_fields(self):
        """Test that all Neo4j settings are optional."""
        settings = Neo4jSettings()
        
        assert settings.uri is None
        assert settings.user is None
        assert settings.password is None
        
    def test_env_var_loading(self):
        """Test loading from environment variables."""
        with patch.dict(os.environ, {
            'NEO4J_URI': 'bolt://localhost:7687',
            'NEO4J_USER': 'neo4j',
            'NEO4J_PASSWORD': 'test-password'
        }):
            settings = Neo4jSettings()
            
            assert settings.uri == 'bolt://localhost:7687'
            assert settings.user == 'neo4j'
            assert settings.password.get_secret_value() == 'test-password'


class TestServerSettings:
    """Test the ServerSettings class."""
    
    def test_default_values(self):
        """Test default server settings."""
        settings = ServerSettings()
        
        assert settings.host == "0.0.0.0"
        assert settings.port == 8051
        
    def test_env_var_loading(self):
        """Test loading from environment variables."""
        with patch.dict(os.environ, {
            'HOST': '127.0.0.1',
            'PORT': '8080'
        }):
            settings = ServerSettings()
            
            assert settings.host == '127.0.0.1'
            assert settings.port == 8080
    
    def test_port_type_conversion(self):
        """Test that port is correctly converted to int."""
        with patch.dict(os.environ, {'PORT': '3000'}):
            settings = ServerSettings()
            assert isinstance(settings.port, int)
            assert settings.port == 3000