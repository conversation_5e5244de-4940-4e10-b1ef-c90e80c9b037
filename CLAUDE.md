# Personal Development Preferences

- **Always use UV for Python projects** - Fast, reliable, deterministic
- Always organize code to prevent directory bloat
- **Never make assumptions about what I think** - Just ask me when you're not sure - we are partners!

## Python Environment Standards

### UV is the Default
- **Project commands**: `uv add`, `uv remove`, `uv run` (not pip)
- **Lock files**: Always commit `uv.lock` for reproducibility
- **Auto-sync**: `uv run` automatically syncs before execution

### Essential UV Commands
```bash
# Daily Development
uv run script.py           	# Run code (auto-syncs)
uv add package              # Add dependency
uv sync                     # After add any dependency

# Project Setup
uv init                     # Create new project
uv python pin >=3.10        # Pin minimum Python version

# Dependency Updates
uv lock --upgrade-package pkg  # Update specific package
uv sync --locked               # CI/CD: sync without lockfile changes
```

# General Principles

## Clean Code Practices
- **Never create test files or single-use files in project root**, and audit the root directory at the beginning of each session to ensure it stays clean. 
- Use descriptive filenames with versions
- Archive or delete temporary work regularly
- Commit only production-ready code

## UV Development Workflow

### Key Practices
- `uv run script.py` - Run code
- **Never use pip** - Use `uv add`/`uv remove` instead
- **Always commit `uv.lock`** - Never commit `.venv/`
- **Use `uv run` for everything** - Scripts, tests, servers
- **Development deps**: `uv add --dev pytest`
- **CI/CD**: Use `uv sync --locked`

## Documentation Lookup via MCP

### Documentation Search (Context7 MCP Server)
When developing or writing code:
- **Always search documentation first**
- Use Context7 to `get-library-docs` for `libraryID` for `topic` with `tokens`
Examples:
- `get-library-docs` "/pydantic/pydantic-ai" "v2 model validation" "15000"
- supabase: `/supabase/supabase`
- pydantic: `/pydantic/pydantic-ai`
- supabase: `/qdrant/qdrant`
- crawl4ai: `/unclecode/crawl4ai`
- fastapi: `/tiangolo/fastapi`
- fastmcp: `/jlowin/fastapi`
- Others: `resolve libraryID` first to find the correct library ID

