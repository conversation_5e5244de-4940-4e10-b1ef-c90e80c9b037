# Crawl4AI MCP Server Refactoring Plan

## Overview

This document outlines a comprehensive refactoring plan to transform the current monolithic Crawl4AI MCP server into a modular, maintainable, and testable architecture.

## Current Issues

### 1. Monolithic Architecture
- **Problem**: `src/crawl4ai_mcp.py` contains 800+ lines handling everything from server setup to crawling logic
- **Impact**: Difficult to maintain, test, and extend

### 2. Mixed Concerns
- **Problem**: RAG strategies, crawling methods, and MCP tools all mixed in one file
- **Impact**: Changes to one feature can break others; hard to understand code flow

### 3. Testing Challenges
- **Problem**: Tightly coupled components make unit testing nearly impossible
- **Impact**: No confidence in changes; bugs harder to catch

### 4. Configuration Scattered
- **Problem**: RAG strategy logic and configuration spread throughout the main file
- **Impact**: Hard to understand what strategies are available and how they work

## Proposed Modular Architecture

```
src/
├── core/
│   ├── __init__.py
│   ├── server.py          # MCP server setup and lifecycle
│   └── context.py         # Application context management
├── strategies/
│   ├── __init__.py
│   ├── base.py           # Base strategy interface
│   ├── manager.py        # Strategy coordination
│   ├── contextual_embeddings.py
│   ├── hybrid_search.py
│   ├── agentic_rag.py
│   ├── reranking.py
│   └── knowledge_graph.py
├── crawlers/
│   ├── __init__.py
│   ├── base.py           # Base crawler interface
│   ├── single_page.py    # Single page crawling
│   ├── smart_crawler.py  # Smart URL detection
│   ├── batch_crawler.py  # Batch processing
│   └── recursive_crawler.py
├── tools/
│   ├── __init__.py
│   ├── crawling_tools.py # Crawling MCP tools
│   ├── rag_tools.py      # RAG MCP tools
│   └── knowledge_graph_tools.py
├── config/
│   ├── __init__.py
│   └── settings.py       # Centralized configuration
├── utils/
│   ├── __init__.py
│   ├── database.py       # Database utilities
│   ├── embeddings.py     # Embedding utilities
│   └── text_processing.py
└── crawl4ai_mcp.py       # Main entry point (minimal)
```

## Detailed Component Design

### 1. Core Module (`src/core/`)

#### `server.py`
- **Purpose**: MCP server creation and configuration
- **Responsibilities**:
  - Initialize FastMCP server
  - Register all tools
  - Handle server lifecycle
- **Key Functions**:
  - `create_mcp_server() -> FastMCP`

#### `context.py`
- **Purpose**: Application context and dependency management
- **Responsibilities**:
  - Manage AsyncWebCrawler lifecycle
  - Initialize Supabase client
  - Coordinate strategy manager
  - Handle cleanup
- **Key Classes**:
  - `Crawl4AIContext` (dataclass)
  - `crawl4ai_lifespan` (async context manager)

### 2. Strategies Module (`src/strategies/`)

#### `base.py`
- **Purpose**: Define strategy interface
- **Key Classes**:
  - `RAGStrategy` (ABC)
- **Key Methods**:
  - `is_enabled() -> bool`
  - `process_crawl_results(results) -> List[Dict]`
  - `enhance_search(query, results) -> List[Dict]`

#### `manager.py`
- **Purpose**: Coordinate multiple strategies
- **Key Classes**:
  - `StrategyManager`
- **Key Methods**:
  - `register_strategy(strategy)`
  - `process_crawl_results(results)`
  - `enhance_search(query, results)`

#### Individual Strategy Files
Each strategy implements the `RAGStrategy` interface:
- `ContextualEmbeddingsStrategy`
- `HybridSearchStrategy`
- `AgenticRAGStrategy`
- `RerankingStrategy`
- `KnowledgeGraphStrategy`

### 3. Crawlers Module (`src/crawlers/`)

#### `base.py`
- **Purpose**: Define crawler interface
- **Key Classes**:
  - `BaseCrawler` (ABC)
- **Key Methods**:
  - `crawl(*args, **kwargs) -> List[Dict]`

#### Individual Crawler Files
- `SinglePageCrawler`: Handle single page crawling
- `SmartCrawler`: Detect URL type and route appropriately
- `BatchCrawler`: Process multiple URLs efficiently
- `RecursiveCrawler`: Follow internal links

### 4. Tools Module (`src/tools/`)

#### Purpose
Split MCP tool registration by functionality:
- `crawling_tools.py`: `crawl_single_page`, `smart_crawl_url`
- `rag_tools.py`: `perform_rag_query`, `get_available_sources`
- `knowledge_graph_tools.py`: Knowledge graph related tools

### 5. Config Module (`src/config/`)

#### `settings.py`
- **Purpose**: Centralized configuration management
- **Key Classes**:
  - `Settings` (dataclass with type hints)
- **Key Functions**:
  - `load_settings() -> Settings`
- **Benefits**:
  - Type safety for configuration
  - Single source of truth
  - Easy testing with different configs

### 6. Utils Module (`src/utils/`)

#### Purpose
Extract reusable utilities:
- `database.py`: Supabase operations
- `embeddings.py`: OpenAI embedding utilities
- `text_processing.py`: Chunking and text processing

## Migration Strategy

### Phase 1: Configuration Extraction (Week 1)
**Goal**: Centralize all configuration management

**Tasks**:
1. Create `src/config/settings.py`
2. Define `Settings` dataclass with all environment variables
3. Implement `load_settings()` function
4. Update main file to use centralized config
5. Add configuration validation

**Success Criteria**:
- All environment variables accessed through `Settings` object
- Configuration validation on startup
- No breaking changes to existing functionality

### Phase 2: Core Infrastructure (Week 2)
**Goal**: Extract server setup and context management

**Tasks**:
1. Create `src/core/server.py` with `create_mcp_server()`
2. Create `src/core/context.py` with `Crawl4AIContext`
3. Move lifecycle management to `crawl4ai_lifespan`
4. Update main entry point to use new core modules
5. Ensure all existing functionality works

**Success Criteria**:
- Clean separation of server setup from business logic
- Context properly manages all dependencies
- All existing tools continue to work

**Add dependency injection pattern:**

```python
@dataclass
class Crawl4AIContext:
    crawler: AsyncWebCrawler
    supabase_client: Client  
    settings: Settings
    _strategy_manager: Optional['StrategyManager'] = None
    
    def get_strategy_manager(self) -> 'StrategyManager':
        """Lazy load strategy manager to avoid circular imports."""
        if self._strategy_manager is None:
            from ..strategies.manager import StrategyManager
            self._strategy_manager = StrategyManager(self.settings)
        return self._strategy_manager
```

### Phase 3: Strategy Pattern Implementation (Week 3-4)
**Goal**: Extract RAG strategies into pluggable components

**Tasks**:
1. Create `src/strategies/base.py` with `RAGStrategy` interface
2. Create `src/strategies/manager.py` to coordinate strategies  
3. Extract strategies:
   - contextual_embeddings.py: Contextual query enhancement
   - hybrid_search.py: BM25 + semantic search logic
   - agentic_rag.py: Code extraction and analysis
   - reranking.py: Cross-encoder reranking
   - knowledge_graph.py: Neo4j integration

**Add these interface definitions:**

```python
# src/strategies/base.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any

class RAGStrategy(ABC):
    @abstractmethod
    def is_enabled(self) -> bool:
        """Check if strategy is enabled via settings."""
        pass
    
    @abstractmethod
    async def enhance_search(self, query: str, results: List[Dict]) -> List[Dict]:
        """Process and enhance search results."""
        pass

# src/crawlers/base.py  
class BaseCrawler(ABC):
    @abstractmethod
    async def crawl(self, url: str, **kwargs) -> List[Dict[str, Any]]:
        """Crawl the given URL and return structured results."""
        pass
```

**Success Criteria**:
- Each strategy can be enabled/disabled independently
- New strategies can be added without modifying existing code
- All existing RAG functionality preserved

### Phase 4: Crawler Modularization (Week 5)
**Goal**: Extract crawling logic into specialized crawlers

**Tasks**:
1. Create `src/crawlers/base.py` with `BaseCrawler` interface
2. Extract crawling methods into separate crawler classes
3. Implement smart routing in `SmartCrawler`
4. Update tools to use appropriate crawler instances
5. Add crawler-specific configuration options

**Success Criteria**:
- Each crawler handles specific URL types
- Easy to add new crawler types
- Crawling performance maintained or improved

### Phase 5: Tools Reorganization (Week 6)
**Goal**: Organize MCP tools by functionality

**Tasks**:
1. Create separate tool registration files
2. Move tools to appropriate modules
3. Implement `register_all_tools()` coordinator
4. Ensure proper dependency injection
5. Add tool-specific error handling

**Success Criteria**:
- Tools logically grouped by functionality
- Easy to add new tools
- Clean separation of concerns

### Phase 6: Utilities and Testing (Week 7-8)
**Goal**: Extract utilities and add comprehensive testing

**Tasks**:
1. Create `src/utils/` module with reusable components
2. Add unit tests for each module
3. Add integration tests for key workflows
4. Add performance benchmarks
5. Update documentation

**Success Criteria**:
- >80% test coverage
- All modules have unit tests
- Performance maintained or improved
- Documentation updated

## Testing Strategy Per Phase

### Phase 2: Core Infrastructure
**Tests to Add:**
- Unit tests for `Crawl4AIContext` initialization
- Integration tests for server startup/shutdown
- Regression tests: All existing MCP tools still work

### Phase 3: Strategy Pattern  
**Tests to Add:**
- Unit tests for each strategy in isolation
- Unit tests for `StrategyManager` coordination
- Integration tests for strategy combinations
- Performance benchmarks: Strategy overhead < 10ms

### Phase 4: Crawler Modularization
**Tests to Add:**
- Unit tests for each crawler type
- Integration tests for `SmartCrawler` URL detection
- End-to-end tests for sitemap vs single page crawling

### Phase 5: Tools Reorganization
**Tests to Add:**
- Unit tests for each tool module
- Integration tests for tool registration
- MCP protocol compliance tests

### Phase 6: Utilities Enhancement
**Tests to Add:**
- Unit tests for all utility functions
- Performance tests for database operations
- Memory usage tests for embedding operations

**Target Metrics:**
- >80% test coverage by end of Phase 6
- All existing functionality preserved (regression tests)
- Performance maintained or improved

## Success Criteria Per Phase

### Phase 2: Core Infrastructure
✅ **Must Have:**
- All existing MCP tools work unchanged
- Server startup time < 5 seconds
- Memory usage not increased >10%

### Phase 3: Strategy Pattern
✅ **Must Have:**  
- Each strategy can be enabled/disabled independently
- RAG query performance maintained
- New strategy can be added in <50 lines of code

### Phase 4: Crawler Modularization
✅ **Must Have:**
- All URL types (sitemap, single page, robots.txt) work
- Crawling performance maintained
- Smart detection accuracy >95%

### Phase 5: Tools Reorganization  
✅ **Must Have:**
- All MCP tools accessible and functional
- Tool registration time <1 second
- Clean separation of concerns achieved

### Phase 6: Utilities Enhancement
✅ **Must Have:**
- >80% test coverage achieved
- Main file reduced to <100 lines
- Documentation updated and complete

## Benefits of Modular Architecture

### 1. Maintainability
- **Single Responsibility**: Each module has one clear purpose
- **Loose Coupling**: Modules interact through well-defined interfaces
- **High Cohesion**: Related functionality grouped together

### 2. Testability
- **Unit Testing**: Each component can be tested in isolation
- **Mocking**: Clear interfaces make mocking dependencies easy
- **Integration Testing**: Well-defined boundaries for integration tests

### 3. Extensibility
- **Plugin Architecture**: New strategies and crawlers can be added easily
- **Configuration Driven**: Features can be enabled/disabled via config
- **Interface Based**: New implementations can replace existing ones

### 4. Performance
- **Lazy Loading**: Only load components that are needed
- **Parallel Processing**: Independent modules can run concurrently
- **Resource Management**: Better control over resource usage

### 5. Developer Experience
- **Code Navigation**: Easier to find relevant code
- **Debugging**: Smaller, focused modules are easier to debug
- **Onboarding**: New developers can understand individual modules

## Rollback Strategy

### Phase-by-Phase Rollback Plans

**Phase 2 Rollback:**
- If context management fails: Revert to inline initialization
- If server extraction breaks: Keep server setup in main file
- **Trigger**: Any existing MCP tool fails

**Phase 3 Rollback:**  
- If strategy extraction breaks functionality: Keep strategies inline
- If performance degrades >20%: Revert to direct implementation
- **Trigger**: RAG query performance or accuracy drops

**Phase 4 Rollback:**
- If crawler modularization breaks crawling: Revert to monolithic crawling
- If smart detection fails: Keep URL detection in main logic
- **Trigger**: Any crawl type fails or performance degrades

**General Rollback Procedure:**
1. Revert to previous git commit
2. Run full regression test suite  
3. Deploy previous working version
4. Analyze failure and adjust approach

## Risk Mitigation

### 1. Breaking Changes
- **Mitigation**: Maintain backward compatibility during migration
- **Strategy**: Keep existing API surface unchanged
- **Testing**: Comprehensive regression testing

### 2. Performance Regression
- **Mitigation**: Benchmark before and after each phase
- **Strategy**: Profile critical paths during refactoring
- **Monitoring**: Add performance metrics to key operations

### 3. Complexity Increase
- **Mitigation**: Keep interfaces simple and well-documented
- **Strategy**: Follow established patterns (Strategy, Factory, etc.)
- **Review**: Regular architecture reviews during migration

## Success Metrics

### Code Quality
- **Lines of Code**: Reduce main file from 800+ to <100 lines
- **Cyclomatic Complexity**: Reduce average complexity per function
- **Test Coverage**: Achieve >80% test coverage

### Maintainability
- **Time to Add Feature**: Reduce time to add new RAG strategy
- **Bug Fix Time**: Reduce time to locate and fix bugs
- **Onboarding Time**: Reduce time for new developers to contribute

### Performance
- **Startup Time**: Maintain or improve server startup time
- **Memory Usage**: Maintain or reduce memory footprint
- **Response Time**: Maintain or improve tool response times

## Timeline Summary

| Phase | Duration | Focus | Deliverables |
|-------|----------|-------|--------------|
| 1 | Week 1 | Configuration | Centralized settings |
| 2 | Week 2 | Core Infrastructure | Server & context modules |
| 3 | Week 3-4 | Strategy Pattern | Pluggable RAG strategies |
| 4 | Week 5 | Crawler Modularization | Specialized crawlers |
| 5 | Week 6 | Tools Reorganization | Organized MCP tools |
| 6 | Week 7-8 | Testing & Polish | Tests, docs, benchmarks |

**Total Duration**: 8 weeks
**Risk Buffer**: 2 weeks
**Target Completion**: 10 weeks

## Next Steps

1. **Review and Approve**: Review this plan and provide feedback
2. **Environment Setup**: Set up development and testing environments
3. **Baseline Metrics**: Establish current performance and quality metrics
4. **Phase 1 Kickoff**: Begin with configuration extraction
5. **Regular Check-ins**: Weekly progress reviews and adjustments

---

*This refactoring plan transforms the Crawl4AI MCP server from a monolithic application into a modular, maintainable, and extensible system while preserving all existing functionality.*
