# Crawl4AI MCP Server Documentation

Welcome to the comprehensive documentation for the modular Crawl4AI MCP server architecture.

## Documentation Structure

### 📚 [Migration Guide](./MIGRATION_GUIDE.md)
Step-by-step guide for migrating from the monolithic `crawl4ai_mcp.py` to the new modular architecture. Includes:
- Quick start instructions
- Architecture comparison
- Migration steps
- Troubleshooting tips

### 🔧 [API Reference](./API_REFERENCE.md)
Complete API documentation for all modules, including:
- Configuration management
- Core infrastructure components
- Strategy interfaces and implementations
- Crawler types and usage
- MCP tool specifications
- Code examples and best practices

### 🏗️ [Architecture Documentation](../CONTEXT.md)
Detailed project context and architecture decisions, including:
- Project overview and status
- Technical architecture diagrams
- Design patterns implemented
- Performance metrics
- Development workflow

### ✅ [Task Tracking](../TASKS.md)
Current project status and task tracking:
- Completed phases (1-6)
- Success metrics
- Technical debt tracking
- Quality gates

## Quick Links

### For Users
- **Run original version**: `uv run src/crawl4ai_mcp.py`
- **Run modular version**: `uv run src/main.py`
- Both versions provide identical MCP tools

### For Developers
- **Add new strategy**: See [API Reference - Strategies](./API_REFERENCE.md#strategies)
- **Add new crawler**: See [API Reference - Crawlers](./API_REFERENCE.md#crawlers)
- **Add new MCP tool**: See [API Reference - MCP Tools](./API_REFERENCE.md#mcp-tools)

## Key Benefits of Modular Architecture

1. **Maintainability**: 27 focused modules vs 1 monolithic file
2. **Testability**: 34+ unit and integration tests
3. **Performance**: 0.011s startup (vs 5s target)
4. **Extensibility**: Easy to add features without touching core
5. **Main file**: Reduced from 1,853 to 65 lines (96.5% reduction)

## Getting Started

1. **Check requirements**:
   ```bash
   # Python 3.10+ required
   python --version
   
   # UV package manager
   uv --version
   ```

2. **Install dependencies**:
   ```bash
   uv sync
   ```

3. **Set environment variables**:
   ```bash
   export SUPABASE_URL=your-url
   export SUPABASE_SERVICE_KEY=your-key
   export OPENAI_API_KEY=your-key
   ```

4. **Run the server**:
   ```bash
   # Modular version
   uv run src/main.py
   
   # Or original version
   uv run src/crawl4ai_mcp.py
   ```

## Testing

```bash
# Run all tests
uv run pytest tests/

# Run with coverage
uv run pytest tests/ --cov=src

# Run specific test category
uv run pytest tests/integration/
```

## Support

For questions or issues:
1. Check the [Migration Guide](./MIGRATION_GUIDE.md) for common issues
2. Review the [API Reference](./API_REFERENCE.md) for usage examples
3. See test files for implementation patterns

## Project Statistics

- **Total modules**: 27
- **Lines of code**: ~5,200+ (modular) vs 1,853 (monolithic)
- **Test files**: 12+ with comprehensive coverage
- **Performance**: All metrics met or exceeded
- **Backward compatibility**: 100% maintained