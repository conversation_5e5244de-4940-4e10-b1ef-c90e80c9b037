# Crawl4AI MCP Server Setup Guide

## Claude Desktop Configuration

### 1. Locate Claude Desktop Settings

On macOS, the configuration file is located at:
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

### 2. Add MCP Server Configuration

Add the following to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "crawl4ai": {
      "command": "uv",
      "args": ["run", "src/main.py"],
      "cwd": "/Users/<USER>/research/crawl4context",
      "env": {
        "TRANSPORT": "sse",
        "HOST": "localhost", 
        "PORT": "8051",
        "OPENAI_API_KEY": "your-openai-api-key",
        "MODEL_CHOICE": "gpt-3.5-turbo",
        "USE_CONTEXTUAL_EMBEDDINGS": "true",
        "USE_HYBRID_SEARCH": "true",
        "USE_AGENTIC_RAG": "true",
        "USE_RERANKING": "true",
        "USE_KNOWLEDGE_GRAPH": "false",
        "SUPABASE_URL": "your-supabase-url",
        "SUPABASE_SERVICE_KEY": "your-supabase-service-key"
      }
    }
  }
}
```

### 3. Restart Claude Desktop

After saving the configuration, restart Claude Desktop. The Crawl4AI tools should now be available in your conversations.

## VS Code Configuration (using Continue.dev)

### 1. Install Continue Extension

Install the Continue extension from the VS Code marketplace.

### 2. Configure MCP in Continue

Add to your Continue configuration (`~/.continue/config.json`):

```json
{
  "models": [
    // Your existing models...
  ],
  "mcpServers": {
    "crawl4ai": {
      "command": "uv",
      "args": ["run", "src/main.py"],
      "cwd": "/Users/<USER>/research/crawl4context",
      "env": {
        "TRANSPORT": "sse",
        "HOST": "localhost", 
        "PORT": "8051",
        "OPENAI_API_KEY": "your-openai-api-key",
        "MODEL_CHOICE": "gpt-3.5-turbo",
        "USE_CONTEXTUAL_EMBEDDINGS": "true",
        "USE_HYBRID_SEARCH": "true",
        "USE_AGENTIC_RAG": "true",
        "USE_RERANKING": "true",
        "USE_KNOWLEDGE_GRAPH": "false",
        "SUPABASE_URL": "your-supabase-url",
        "SUPABASE_SERVICE_KEY": "your-supabase-service-key"
      }
    }
  }
}
```

## Connecting to Running Server (Recommended)

Since your server is already running with `uv run src/main.py` on port 8051 using SSE transport, you can connect Claude Desktop to the existing server:

### Claude Desktop (SSE connection to running server)
```json
{
  "mcpServers": {
    "crawl4ai": {
      "url": "sse://localhost:8051/sse"
    }
  }
}
```

**Note**: This connects to your already-running server instead of spawning a new process.
```

### Environment Variables

The server uses these environment variables (from `.env` file):

```bash
# Transport and server settings
TRANSPORT=sse
HOST=localhost
PORT=8051

# OpenAI API for embeddings and LLM operations
OPENAI_API_KEY=your-openai-api-key
MODEL_CHOICE=gpt-3.5-turbo

# RAG enhancement features
USE_CONTEXTUAL_EMBEDDINGS=true
USE_HYBRID_SEARCH=true
USE_AGENTIC_RAG=true
USE_RERANKING=true
USE_KNOWLEDGE_GRAPH=false

# Optional: Supabase for data storage
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_KEY=your-supabase-service-key

# Optional: Neo4j for knowledge graphs (if USE_KNOWLEDGE_GRAPH=true)
NEO4J_URI=neo4j://127.0.0.1:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your-neo4j-password
```

## Available Tools

Once configured, you'll have access to these tools:

1. **crawl** - Advanced web crawling with JavaScript support
2. **crawl_static** - Fast HTML-only crawling
3. **screenshot** - Capture webpage screenshots
4. **extract_llm** - AI-powered content extraction
5. **extract_knowledge_graph** - Generate knowledge graphs
6. **check_hallucination** - Verify AI-generated content
7. **sql_query** - Query crawled data

## Testing the Connection

After configuration, you can test by asking Claude to:
- "Use crawl4ai to get the content from https://example.com"
- "Take a screenshot of https://example.com"
- "Extract the main content from https://example.com as markdown"