# Modular Refactoring Implementation Plan

## Current Status
✅ Phase 1: Configuration Extraction - COMPLETED
- Created `src/config/` module with centralized settings
- Ready to be used by other modules

## Next Steps: Phase 2-6 Implementation

### Phase 2: Core Infrastructure (Current Focus)

#### 1. Create `src/core/server.py`
- Extract MCP server initialization from main file
- Move server creation logic
- Handle tool registration

#### 2. Create `src/core/context.py`
- Move `Crawl4AIContext` dataclass
- Extract `crawl4ai_lifespan` context manager
- Centralize dependency management

### Phase 3: Strategy Pattern Implementation

#### 1. Create `src/strategies/base.py`
- Define `RAGStrategy` abstract base class
- Common interface for all strategies

#### 2. Extract Individual Strategies
- `contextual_embeddings.py`: Contextual embedding logic
- `hybrid_search.py`: BM25 + semantic search
- `agentic_rag.py`: Code extraction and analysis
- `reranking.py`: Cross-encoder reranking
- `knowledge_graph.py`: Neo4j integration

#### 3. <PERSON>reate `src/strategies/manager.py`
- Coordinate multiple strategies
- Handle strategy selection based on settings

### Phase 4: Crawler Modularization

#### 1. <PERSON>reate `src/crawlers/base.py`
- Define `BaseCrawler` interface
- Common crawling functionality

#### 2. Extract Crawler Types
- `single_page.py`: Basic webpage crawling
- `smart_crawler.py`: URL type detection and routing
- `batch_crawler.py`: Multiple URL processing
- `sitemap_crawler.py`: Sitemap parsing
- `robots_txt_crawler.py`: robots.txt handling

### Phase 5: Tools Reorganization

#### 1. Split MCP tools by domain
- `src/tools/crawling_tools.py`: Web crawling tools
- `src/tools/rag_tools.py`: RAG and search tools
- `src/tools/knowledge_graph_tools.py`: Neo4j tools
- `src/tools/hallucination_tools.py`: AI validation tools

### Phase 6: Utilities Enhancement

#### 1. Organize utility functions
- `src/utils/database.py`: Supabase operations
- `src/utils/embeddings.py`: Embedding generation
- `src/utils/text_processing.py`: Chunking and processing
- `src/utils/ai_helpers.py`: OpenAI/AI operations

## Implementation Order

1. **Start with Phase 2** - Core infrastructure sets the foundation
2. **Then Phase 5** - Tools reorganization (easier, mechanical refactoring)
3. **Then Phase 3** - Strategy pattern (more complex, needs design)
4. **Then Phase 4** - Crawler modularization
5. **Finally Phase 6** - Utility cleanup

## Key Principles

1. **Maintain backward compatibility** - Don't break existing functionality
2. **Test after each extraction** - Ensure nothing breaks
3. **Keep interfaces simple** - Don't over-engineer
4. **Document as you go** - Add docstrings and type hints

Ready to start with Phase 2?