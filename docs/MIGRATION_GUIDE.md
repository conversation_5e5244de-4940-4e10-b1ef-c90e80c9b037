# Crawl4AI MCP Server Migration Guide

This guide helps you migrate from the monolithic `crawl4ai_mcp.py` to the new modular architecture.

## Quick Start

### For Users (No Code Changes Required)

1. **Using the original file** (no changes needed):
   ```bash
   uv run src/crawl4ai_mcp.py
   ```

2. **Using the new modular version**:
   ```bash
   uv run src/main.py
   ```

Both versions provide identical functionality through the same MCP tools.

### For Developers

The new architecture provides better maintainability, testability, and extensibility while preserving all functionality.

## Architecture Overview

### Before (Monolithic)
```
src/crawl4ai_mcp.py  # 1,853 lines - Everything in one file
```

### After (Modular)
```
src/
├── main.py              # 65 lines - Clean entry point
├── config/              # Configuration management
├── core/                # Core infrastructure
├── strategies/          # RAG enhancement strategies
├── crawlers/            # URL crawling implementations
└── tools/               # MCP tool definitions
```

## Key Changes

### 1. Configuration Management

**Before:**
```python
# Scattered throughout the file
SUPABASE_URL = os.getenv("SUPABASE_URL")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
USE_CONTEXTUAL_EMBEDDINGS = os.getenv("USE_CONTEXTUAL_EMBEDDINGS", "true").lower() == "true"
```

**After:**
```python
from src.config.settings import Settings

settings = Settings()  # All configuration centralized
# Access: settings.supabase.url, settings.openai.api_key, etc.
```

### 2. Application Context

**Before:**
```python
# Global variables and manual initialization
supabase_client = None
openai_client = None
strategy_manager = None
```

**After:**
```python
from src.core.application_context import ApplicationContext

context = ApplicationContext(settings)
await context.initialize()
# Access: context.supabase_client, context.openai_client, etc.
```

### 3. Strategy Pattern

**Before:**
```python
# Inline strategy logic mixed with other code
if USE_CONTEXTUAL_EMBEDDINGS:
    # 100+ lines of contextual embedding logic
if USE_HYBRID_SEARCH:
    # 150+ lines of hybrid search logic
```

**After:**
```python
from src.strategies.manager import StrategyManager

# Strategies are modular and pluggable
strategy_manager = StrategyManager(context)
await strategy_manager.initialize()
results = await strategy_manager.process(query, results)
```

### 4. Crawler System

**Before:**
```python
# Single crawl function with complex branching
async def crawl_url(url: str) -> Dict:
    if url.endswith('.xml'):
        # Sitemap logic
    elif url.endswith('.txt'):
        # Text file logic
    else:
        # Webpage logic
```

**After:**
```python
from src.crawlers.manager import CrawlerManager

# Specialized crawlers with automatic routing
crawler_manager = CrawlerManager()
result = await crawler_manager.crawl(url)  # Automatically selects right crawler
```

### 5. MCP Tools

**Before:**
```python
# All 8 tools defined in one file
@server.tool()
async def crawl_single_page(...): ...

@server.tool()
async def perform_rag_query(...): ...
# ... 6 more tools
```

**After:**
```python
from src.tools import register_all_tools

# Tools organized by domain
register_all_tools(server, context)
# - crawling_tools.py: crawl_single_page, smart_crawl_url
# - rag_tools.py: perform_rag_query, get_available_sources, search_code_examples
# - knowledge_graph_tools.py: query_knowledge_graph, parse_github_repository, check_ai_script_hallucinations
```

## Migration Steps

### Step 1: Update Imports

If you have custom code that imports from `crawl4ai_mcp.py`:

**Before:**
```python
from crawl4ai_mcp import crawl_url, rag_query
```

**After:**
```python
from src.crawlers.manager import CrawlerManager
from src.strategies.manager import StrategyManager

# Use through managers
crawler_manager = CrawlerManager()
result = await crawler_manager.crawl(url)
```

### Step 2: Update Configuration

**Before:**
```python
# Direct environment variable access
api_key = os.getenv("OPENAI_API_KEY")
```

**After:**
```python
from src.config.settings import Settings

settings = Settings()
api_key = settings.openai.api_key
```

### Step 3: Use Application Context

**Before:**
```python
# Manual client initialization
supabase = create_client(url, key)
openai = OpenAI(api_key=api_key)
```

**After:**
```python
from src.core.application_context import ApplicationContext

context = ApplicationContext(settings)
await context.initialize()
# Clients available as: context.supabase_client, context.openai_client
```

## Adding New Features

### Adding a New Strategy

1. Create a new file in `src/strategies/`:
```python
# src/strategies/my_strategy.py
from .base_types import RAGStrategy, RAGResult

class MyStrategy(RAGStrategy):
    async def initialize(self):
        # Setup code
        pass
    
    async def process(self, query: str, results: List[RAGResult], context: Any) -> List[RAGResult]:
        # Process results
        return results
    
    async def cleanup(self):
        # Cleanup code
        pass
```

2. Register in `src/strategies/manager.py`:
```python
from .my_strategy import MyStrategy

# In _initialize_strategies():
strategy_classes = [
    # ... existing strategies
    MyStrategy
]
```

### Adding a New Crawler

1. Create a new file in `src/crawlers/`:
```python
# src/crawlers/my_crawler.py
from .base_types import BaseCrawler, CrawlResult, URLType

class MyCrawler(BaseCrawler):
    def can_handle(self, url: str, url_type: URLType) -> bool:
        return url.endswith('.myformat')
    
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        # Crawling logic
        return CrawlResult(url=url, content=content)
```

2. Register in `src/crawlers/manager.py`:
```python
from .my_crawler import MyCrawler

# In __init__():
self.crawlers.append(MyCrawler())
```

### Adding a New MCP Tool

1. Add to appropriate tool file or create new one:
```python
# src/tools/my_tools.py
from typing import Dict, Any

async def my_new_tool(server, context, **params) -> Dict[str, Any]:
    """Tool description."""
    # Implementation
    return {"result": "data"}
```

2. Register in `src/tools/__init__.py`:
```python
from .my_tools import my_new_tool

# In register_all_tools():
@server.tool()
async def my_new_tool_wrapper(**params):
    return await my_new_tool(server, context, **params)
```

## Testing

### Unit Testing
```bash
# Run all tests
uv run pytest tests/

# Run specific test module
uv run pytest tests/strategies/test_manager.py

# Run with coverage
uv run pytest tests/ --cov=src
```

### Integration Testing
```bash
# Run integration tests
uv run pytest tests/integration/

# Run performance benchmarks
uv run pytest tests/integration/test_performance.py -v
```

## Performance Considerations

The modular architecture maintains or improves performance:

- **Server startup**: 0.011s (vs 5s target)
- **Strategy overhead**: 0.0ms when disabled
- **Memory usage**: No increase over baseline
- **Lazy loading**: Components only loaded when needed

## Environment Variables

All environment variables remain the same:

```bash
# Required
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-key
OPENAI_API_KEY=your-openai-key

# Optional (with defaults)
USE_CONTEXTUAL_EMBEDDINGS=true
USE_HYBRID_SEARCH=true
USE_RERANKING=false
USE_AGENTIC_RAG=false
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8051
```

## Troubleshooting

### Import Errors

If you see import errors like:
```
ImportError: cannot import name 'X' from 'crawl4ai_mcp'
```

Update to use the new module structure:
```python
# Instead of importing from crawl4ai_mcp
from src.strategies.manager import StrategyManager
from src.crawlers.manager import CrawlerManager
```

### Configuration Issues

If settings aren't loading:
1. Check environment variables are set
2. Validate with: `uv run python -c "from src.config.settings import Settings; print(Settings().validate())"`

### Performance Issues

If experiencing slow startup:
1. Check which strategies are enabled
2. Review logs for initialization bottlenecks
3. Consider disabling unused strategies

## Benefits of Migration

1. **Maintainability**: Each component has a single responsibility
2. **Testability**: Components can be tested in isolation
3. **Extensibility**: Easy to add new strategies, crawlers, and tools
4. **Performance**: Lazy loading and better resource management
5. **Debugging**: Clear module boundaries make issues easier to trace

## Support

For issues or questions:
1. Check the test files for usage examples
2. Review the architecture documentation in CONTEXT.md
3. Examine the module docstrings for detailed API information