# Crawl4AI MCP Server API Reference

Complete API documentation for the modular Crawl4AI MCP server architecture.

## Table of Contents

1. [Configuration](#configuration)
2. [Core Infrastructure](#core-infrastructure)
3. [Strategies](#strategies)
4. [Crawlers](#crawlers)
5. [MCP Tools](#mcp-tools)
6. [Utilities](#utilities)

---

## Configuration

### `src.config.settings`

#### `Settings`
Main configuration class that aggregates all settings.

```python
from src.config.settings import Settings

settings = Settings()
```

**Attributes:**
- `server: ServerSettings` - Server configuration
- `strategies: StrategySettings` - Strategy feature flags
- `database: DatabaseSettings` - Database settings
- `ai: AISettings` - AI service settings
- `supabase: SupabaseSettings` - Supabase configuration
- `openai: OpenAISettings` - OpenAI configuration
- `neo4j: Neo4jSettings` - Neo4j configuration

**Methods:**
- `validate() -> ValidationResult` - Validate all settings
- `to_dict() -> Dict[str, Any]` - Export settings as dictionary

#### `ValidationResult`
```python
@dataclass
class ValidationResult:
    is_valid: bool
    errors: List[str]
    warnings: List[str]
```

---

## Core Infrastructure

### `src.core.application_context`

#### `ApplicationContext`
Central dependency injection container with lazy loading.

```python
from src.core.application_context import ApplicationContext

context = ApplicationContext(settings)
await context.initialize()
```

**Properties (lazy-loaded):**
- `strategy_manager: StrategyManager` - RAG strategy coordinator
- `crawler_manager: CrawlerManager` - URL crawler coordinator
- `supabase_client: Client` - Supabase client instance
- `openai_client: OpenAI` - OpenAI client instance

**Methods:**
- `async initialize()` - Initialize the context
- `async cleanup()` - Clean up all resources

### `src.core.modular_server`

#### `create_server(settings, context=None) -> FastMCP`
Create and configure the MCP server.

```python
from src.core.modular_server import create_server

server = create_server(settings, context)
await server.run()
```

**Parameters:**
- `settings: Settings` - Application settings
- `context: Optional[ApplicationContext]` - Pre-initialized context

**Returns:**
- `FastMCP` - Configured MCP server instance

---

## Strategies

### `src.strategies.base_types`

#### `RAGStrategy` (ABC)
Base interface for all RAG enhancement strategies.

```python
class RAGStrategy(ABC):
    @abstractmethod
    async def initialize(self):
        """Initialize strategy resources."""
        pass
    
    @abstractmethod
    async def process(self, query: str, results: List[RAGResult], context: Any) -> List[RAGResult]:
        """Process search results."""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """Clean up strategy resources."""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Get strategy name."""
        pass
    
    @abstractmethod
    def is_enabled(self) -> bool:
        """Check if strategy is enabled."""
        pass
```

#### `RAGResult`
```python
@dataclass
class RAGResult:
    title: str
    url: str
    content: str
    relevance_score: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    strategies_applied: List[str] = field(default_factory=list)
```

### `src.strategies.manager`

#### `StrategyManager`
Coordinates multiple RAG strategies.

```python
from src.strategies.manager import StrategyManager

manager = StrategyManager(context)
await manager.initialize()
```

**Methods:**
- `async initialize()` - Initialize all enabled strategies
- `async process(query: str, results: List[RAGResult]) -> List[RAGResult]` - Apply all strategies
- `get_enabled_strategies() -> List[str]` - Get list of enabled strategy names
- `is_strategy_enabled(name: str) -> bool` - Check if specific strategy is enabled
- `async cleanup()` - Clean up all strategies

### Available Strategies

#### `ContextualEmbeddingsStrategy`
Enhances search with contextual embeddings.

**Configuration:**
- `USE_CONTEXTUAL_EMBEDDINGS=true/false`

#### `HybridSearchStrategy`
Combines BM25 and vector search.

**Configuration:**
- `USE_HYBRID_SEARCH=true/false`

#### `RerankingStrategy`
Re-ranks results using cross-encoder models.

**Configuration:**
- `USE_RERANKING=true/false`

#### `AgenticRAGStrategy`
Extracts and analyzes code from results.

**Configuration:**
- `USE_AGENTIC_RAG=true/false`

---

## Crawlers

### `src.crawlers.base_types`

#### `BaseCrawler` (ABC)
Base interface for all crawler implementations.

```python
class BaseCrawler(ABC):
    @abstractmethod
    def can_handle(self, url: str, url_type: URLType) -> bool:
        """Check if crawler can handle URL."""
        pass
    
    @abstractmethod
    async def crawl(self, url: str, **kwargs) -> CrawlResult:
        """Crawl the URL and return result."""
        pass
    
    def get_name(self) -> str:
        """Get crawler name."""
        return self.__class__.__name__
```

#### `CrawlResult`
```python
@dataclass
class CrawlResult:
    url: str
    content: str
    title: Optional[str] = None
    error: Optional[CrawlError] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    crawled_at: datetime = field(default_factory=datetime.utcnow)
```

#### `CrawlError`
```python
@dataclass
class CrawlError:
    message: str
    error_type: str
    details: Optional[Dict[str, Any]] = None
```

#### `URLType` (Enum)
```python
class URLType(Enum):
    WEBPAGE = "webpage"
    SITEMAP = "sitemap"
    ROBOTS_TXT = "robots_txt"
    TEXT_FILE = "text_file"
    UNKNOWN = "unknown"
```

### `src.crawlers.manager`

#### `CrawlerManager`
Manages and routes to appropriate crawlers.

```python
from src.crawlers.manager import CrawlerManager

manager = CrawlerManager()
result = await manager.crawl(url)
```

**Methods:**
- `async crawl(url: str, **kwargs) -> CrawlResult` - Crawl single URL
- `async batch_crawl(urls: List[str], **kwargs) -> List[CrawlResult]` - Crawl multiple URLs
- `get_crawler_for_url(url: str) -> Optional[BaseCrawler]` - Get appropriate crawler

### Available Crawlers

#### `SinglePageCrawler`
Basic webpage crawling using Crawl4AI.

**Handles:** Regular web pages

#### `TextFileCrawler`
Handles .txt and .md files.

**Handles:** `.txt`, `.md` files

#### `SitemapCrawler`
Parses XML sitemaps.

**Handles:** `sitemap.xml` files

#### `RobotsTxtCrawler`
Parses robots.txt files.

**Handles:** `robots.txt` files

#### `BatchCrawler`
Parallel crawling of multiple URLs.

**Usage:**
```python
results = await manager.batch_crawl(urls, max_concurrent=5)
```

#### `RecursiveCrawler`
Follows internal links up to specified depth.

**Usage:**
```python
result = await manager.crawl(url, max_depth=2, follow_links=True)
```

#### `SmartCrawler`
Intelligent routing based on URL analysis.

**Features:**
- Automatic URL type detection
- Fallback handling
- Error recovery

---

## MCP Tools

### Tool Categories

#### Crawling Tools (`src.tools.crawling_tools`)

##### `crawl_single_page`
Crawl a single webpage and extract content.

**Parameters:**
- `url: str` - URL to crawl
- `wait_for: Optional[str]` - CSS selector to wait for
- `timeout: Optional[int]` - Timeout in milliseconds
- `screenshot: Optional[bool]` - Take screenshot

**Returns:**
```json
{
  "url": "https://example.com",
  "content": "Page content...",
  "title": "Page Title",
  "metadata": {
    "crawled_at": "2024-01-01T00:00:00Z"
  }
}
```

##### `smart_crawl_url`
Intelligently crawl based on URL type.

**Parameters:**
- `url: str` - URL to crawl
- `max_depth: Optional[int]` - Max depth for recursive crawling
- `follow_links: Optional[bool]` - Follow internal links

#### RAG Tools (`src.tools.rag_tools`)

##### `perform_rag_query`
Execute RAG-enhanced search query.

**Parameters:**
- `query: str` - Search query
- `limit: Optional[int]` - Max results (default: 10)
- `min_relevance: Optional[float]` - Min relevance score (0-1)

**Returns:**
```json
{
  "results": [
    {
      "title": "Result Title",
      "url": "https://example.com",
      "content": "Enhanced content...",
      "relevance_score": 0.95,
      "strategies_applied": ["contextual_embeddings", "hybrid_search"]
    }
  ],
  "total_results": 10,
  "strategies_used": ["contextual_embeddings", "hybrid_search"]
}
```

##### `get_available_sources`
List all indexed sources in the database.

**Returns:**
```json
{
  "sources": [
    {
      "url": "https://example.com",
      "title": "Example Site",
      "last_crawled": "2024-01-01T00:00:00Z",
      "page_count": 42
    }
  ],
  "total_sources": 1
}
```

##### `search_code_examples`
Search for code examples in indexed content.

**Parameters:**
- `query: str` - Code-related search query
- `language: Optional[str]` - Programming language filter
- `limit: Optional[int]` - Max results

#### Knowledge Graph Tools (`src.tools.knowledge_graph_tools`)

##### `query_knowledge_graph`
Query the Neo4j knowledge graph.

**Parameters:**
- `query: str` - Natural language or Cypher query
- `limit: Optional[int]` - Max results

##### `parse_github_repository`
Parse and index a GitHub repository.

**Parameters:**
- `repo_url: str` - GitHub repository URL
- `branch: Optional[str]` - Branch to parse (default: main)

##### `check_ai_script_hallucinations`
Verify AI-generated code for potential issues.

**Parameters:**
- `script: str` - Code to analyze
- `context: Optional[str]` - Additional context

---

## Utilities

### Error Handling

All components use consistent error handling:

```python
try:
    result = await crawler.crawl(url)
except CrawlError as e:
    logger.error(f"Crawl failed: {e.message}")
    # Returns CrawlResult with error field populated
```

### Logging

Structured logging throughout:

```python
import logging
logger = logging.getLogger(__name__)

logger.info("Starting crawl", extra={"url": url, "crawler": "SinglePageCrawler"})
```

### Performance Monitoring

Built-in performance tracking:

```python
# In strategies
start_time = time.time()
results = await strategy.process(query, results)
duration = time.time() - start_time
logger.info(f"Strategy completed in {duration:.3f}s")
```

---

## Examples

### Basic Crawling

```python
from src.config.settings import Settings
from src.crawlers.manager import CrawlerManager

# Initialize
settings = Settings()
crawler_manager = CrawlerManager()

# Crawl single page
result = await crawler_manager.crawl("https://example.com")
print(f"Title: {result.title}")
print(f"Content: {result.content[:200]}...")
```

### RAG Query with Strategies

```python
from src.core.application_context import ApplicationContext
from src.strategies.base_types import RAGResult

# Initialize
context = ApplicationContext(settings)
await context.initialize()

# Create initial results
results = [
    RAGResult("Page 1", "url1", "content1", 0.8),
    RAGResult("Page 2", "url2", "content2", 0.7)
]

# Process through strategies
enhanced = await context.strategy_manager.process("python tutorial", results)

# Check applied strategies
for result in enhanced:
    print(f"URL: {result.url}")
    print(f"Strategies: {', '.join(result.strategies_applied)}")
    print(f"Score: {result.relevance_score}")
```

### Custom Strategy Implementation

```python
from src.strategies.base_types import RAGStrategy, RAGResult
from typing import List, Any

class CustomStrategy(RAGStrategy):
    def __init__(self, context):
        self.context = context
        self.name = "custom_strategy"
    
    async def initialize(self):
        # Setup any resources
        pass
    
    async def process(self, query: str, results: List[RAGResult], context: Any) -> List[RAGResult]:
        # Custom processing logic
        for result in results:
            result.relevance_score *= 1.2  # Boost scores
            result.strategies_applied.append(self.name)
        return results
    
    async def cleanup(self):
        # Cleanup resources
        pass
    
    def get_name(self) -> str:
        return self.name
    
    def is_enabled(self) -> bool:
        return True  # Or check configuration
```

### Error Recovery

```python
from src.crawlers.base_types import CrawlError

# Crawl with error handling
result = await crawler_manager.crawl("https://invalid-url.com")

if result.error:
    print(f"Error: {result.error.message}")
    print(f"Type: {result.error.error_type}")
    
    # Implement retry logic
    if result.error.error_type == "CONNECTION_ERROR":
        await asyncio.sleep(1)
        result = await crawler_manager.crawl("https://invalid-url.com")
```

---

## Best Practices

1. **Always use context managers** for resource management
2. **Check validation results** before starting the server
3. **Enable only needed strategies** to optimize performance
4. **Use appropriate crawlers** based on URL type
5. **Handle errors gracefully** with proper logging
6. **Monitor performance** using built-in metrics
7. **Test strategies in isolation** before deployment
8. **Use lazy loading** for better startup performance

---

## Configuration Reference

### Environment Variables

| Variable | Type | Default | Description |
|----------|------|---------|-------------|
| `SUPABASE_URL` | string | Required | Supabase project URL |
| `SUPABASE_SERVICE_KEY` | string | Required | Supabase service key |
| `OPENAI_API_KEY` | string | Required | OpenAI API key |
| `USE_CONTEXTUAL_EMBEDDINGS` | boolean | true | Enable contextual embeddings |
| `USE_HYBRID_SEARCH` | boolean | true | Enable hybrid search |
| `USE_RERANKING` | boolean | false | Enable result reranking |
| `USE_AGENTIC_RAG` | boolean | false | Enable agentic RAG |
| `MCP_SERVER_HOST` | string | 0.0.0.0 | Server host |
| `MCP_SERVER_PORT` | integer | 8051 | Server port |
| `NEO4J_URL` | string | Optional | Neo4j connection URL |
| `NEO4J_USER` | string | neo4j | Neo4j username |
| `NEO4J_PASSWORD` | string | Optional | Neo4j password |

---

## Version Information

- **Architecture Version**: 1.0.0
- **Minimum Python**: 3.10
- **FastMCP Version**: Compatible with latest
- **Crawl4AI Version**: Compatible with latest