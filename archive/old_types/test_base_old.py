"""Unit tests for the base crawler module."""

import pytest
from abc import ABC
from unittest.mock import <PERSON>Mock
from src.crawlers.base import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>r, URLDetector
from src.crawlers.base_types import URLType  # URLType is still in base_types, but we'll migrate it


class TestBaseCrawler:
    """Test the BaseCrawler abstract base class."""
    
    def test_is_abstract(self):
        """Test that BaseCrawler cannot be instantiated directly."""
        with pytest.raises(TypeError):
            BaseCrawler()
    
    def test_interface_methods(self):
        """Test that BaseCrawler defines the required interface."""
        # Check that abstract methods are defined
        assert hasattr(BaseCrawler, 'can_handle')
        assert hasattr(BaseCrawler, 'crawl')
        assert hasattr(BaseCrawler, 'get_name')
        
        # Verify they are abstract
        assert getattr(BaseCrawler.can_handle, '__isabstractmethod__', False)
        assert getattr(BaseCrawler.crawl, '__isabstractmethod__', False)
        assert getattr(BaseCrawler.get_name, '__isabstractmethod__', False)
    
    def test_concrete_implementation(self):
        """Test that a concrete implementation can be created."""
        class TestCrawler(BaseCrawler):
            def can_handle(self, url):
                return True
            
            async def crawl(self, url, options=None):
                return CrawlResult(success=True, url=url,
                    content="Test content",
                    title="Test"
                )
            
            def get_name(self):
                return "test_crawler"
        
        # Should be able to instantiate
        crawler = TestCrawler()
        assert isinstance(crawler, BaseCrawler)
        assert crawler.get_name() == "test_crawler"
        assert crawler.can_handle("https://example.com") is True


class TestCrawlResult:
    """Test the CrawlResult class."""
    
    def test_initialization_minimal(self):
        """Test CrawlResult with minimal parameters."""
        result = CrawlResult(
            success=True,
            url="https://example.com",
            content="Test content"
        )

        assert result.url == "https://example.com"
        assert result.content == "Test content"
        assert result.success is True
        assert result.urls_found is None
        assert result.metadata is None
        assert result.error is None
    
    def test_initialization_full(self):
        """Test CrawlResult with all parameters."""
        metadata = {"key": "value"}
        links = ["https://example.com/page1", "https://example.com/page2"]
        media = {"images": ["img1.jpg", "img2.jpg"]}
        
        result = CrawlResult(
            success=True,
            url="https://example.com",
            content="Test content",
            metadata=metadata
        )
        
        assert result.url == "https://example.com"
        assert result.content == "Test content"
        assert result.title == "Test Title"
        assert result.metadata == metadata
        assert result.links == links
        assert result.media == media
        assert result.error is None
    
    def test_with_error(self):
        """Test CrawlResult with error."""
        error = CrawlError("Failed to fetch", "FETCH_ERROR")
        
        result = CrawlResult(success=True, url="https://example.com",
            content="",
            error=error
        )
        
        assert result.error == error
        assert result.content == ""
    
    def test_modification(self):
        """Test that CrawlResult fields can be modified."""
        result = CrawlResult(success=True, url="https://example.com",
            content="Original"
        )
        
        result.content = "Modified"
        result.title = "New Title"
        result.metadata["added"] = "value"
        result.links.append("https://newlink.com")
        
        assert result.content == "Modified"
        assert result.title == "New Title"
        assert result.metadata["added"] == "value"
        assert "https://newlink.com" in result.links


class TestCrawlError:
    """Test the CrawlError class."""
    
    def test_initialization(self):
        """Test CrawlError initialization."""
        error = CrawlError(
            message="Network timeout",
            error_type="TIMEOUT"
        )
        
        assert error.message == "Network timeout"
        assert error.error_type == "TIMEOUT"
        assert error.details is None
    
    def test_with_details(self):
        """Test CrawlError with details."""
        details = {"status_code": 404, "url": "https://example.com"}
        
        error = CrawlError(
            message="Page not found",
            error_type="NOT_FOUND",
            details=details
        )
        
        assert error.message == "Page not found"
        assert error.error_type == "NOT_FOUND"
        assert error.details == details
    
    def test_string_representation(self):
        """Test string representation of CrawlError."""
        error = CrawlError("Test error", "TEST_TYPE")
        
        str_repr = str(error)
        assert "Test error" in str_repr
        assert "TEST_TYPE" in str_repr


class TestURLType:
    """Test the URLType enum."""
    
    def test_enum_values(self):
        """Test that all URL types are defined."""
        assert URLType.WEBPAGE.value == "webpage"
        assert URLType.SITEMAP.value == "sitemap"
        assert URLType.ROBOTS_TXT.value == "robots_txt"
        assert URLType.TEXT_FILE.value == "text_file"
        assert URLType.UNKNOWN.value == "unknown"
    
    def test_enum_members(self):
        """Test enum member access."""
        assert URLType["WEBPAGE"] == URLType.WEBPAGE
        assert URLType["SITEMAP"] == URLType.SITEMAP
        assert URLType["ROBOTS_TXT"] == URLType.ROBOTS_TXT
        assert URLType["TEXT_FILE"] == URLType.TEXT_FILE
        assert URLType["UNKNOWN"] == URLType.UNKNOWN


class TestURLDetector:
    """Test the URLDetector class."""
    
    def test_webpage_detection(self):
        """Test detection of regular webpages."""
        detector = URLDetector()
        
        assert detector.detect_url_type("https://example.com") == URLType.WEBPAGE
        assert detector.detect_url_type("https://example.com/page") == URLType.WEBPAGE
        assert detector.detect_url_type("https://example.com/page.html") == URLType.WEBPAGE
        assert detector.detect_url_type("https://example.com/page.htm") == URLType.WEBPAGE
        assert detector.detect_url_type("https://example.com/page.php") == URLType.WEBPAGE
    
    def test_sitemap_detection(self):
        """Test detection of sitemap URLs."""
        detector = URLDetector()
        
        assert detector.detect_url_type("https://example.com/sitemap.xml") == URLType.SITEMAP
        assert detector.detect_url_type("https://example.com/sitemap_index.xml") == URLType.SITEMAP
        assert detector.detect_url_type("https://example.com/path/sitemap.xml") == URLType.SITEMAP
        assert detector.detect_url_type("https://example.com/sitemap-posts.xml") == URLType.SITEMAP
    
    def test_robots_txt_detection(self):
        """Test detection of robots.txt URLs."""
        detector = URLDetector()
        
        assert detector.detect_url_type("https://example.com/robots.txt") == URLType.ROBOTS_TXT
        assert detector.detect_url_type("http://example.com/robots.txt") == URLType.ROBOTS_TXT
        assert detector.detect_url_type("https://subdomain.example.com/robots.txt") == URLType.ROBOTS_TXT
    
    def test_text_file_detection(self):
        """Test detection of text file URLs."""
        detector = URLDetector()
        
        assert detector.detect_url_type("https://example.com/file.txt") == URLType.TEXT_FILE
        assert detector.detect_url_type("https://example.com/readme.md") == URLType.TEXT_FILE
        assert detector.detect_url_type("https://example.com/document.text") == URLType.TEXT_FILE
        assert detector.detect_url_type("https://example.com/notes.markdown") == URLType.TEXT_FILE
    
    def test_case_insensitive_detection(self):
        """Test that detection is case-insensitive."""
        detector = URLDetector()
        
        assert detector.detect_url_type("https://example.com/SITEMAP.XML") == URLType.SITEMAP
        assert detector.detect_url_type("https://example.com/ROBOTS.TXT") == URLType.ROBOTS_TXT
        assert detector.detect_url_type("https://example.com/README.MD") == URLType.TEXT_FILE
    
    def test_unknown_detection(self):
        """Test detection of unknown URL types."""
        detector = URLDetector()
        
        assert detector.detect_url_type("https://example.com/file.pdf") == URLType.UNKNOWN
        assert detector.detect_url_type("https://example.com/image.jpg") == URLType.UNKNOWN
        assert detector.detect_url_type("https://example.com/video.mp4") == URLType.UNKNOWN
    
    def test_edge_cases(self):
        """Test edge cases in URL detection."""
        detector = URLDetector()
        
        # URLs without extensions default to webpage
        assert detector.detect_url_type("https://example.com/path/to/page") == URLType.WEBPAGE
        
        # Query parameters shouldn't affect detection
        assert detector.detect_url_type("https://example.com/sitemap.xml?param=value") == URLType.SITEMAP
        
        # Fragments shouldn't affect detection
        assert detector.detect_url_type("https://example.com/robots.txt#section") == URLType.ROBOTS_TXT