"""Unit tests for the strategy manager module."""

import pytest
from unittest.mock import <PERSON><PERSON>ock, AsyncMock, patch
from src.strategies.manager import StrategyManager
from src.strategies.base_types import RAGStrategy, RAGResult
from src.core.application_context import ApplicationContext


class MockStrategy(RAGStrategy):
    """Mock strategy for testing."""
    
    def __init__(self, name, enabled=True):
        self.name = name
        self.enabled = enabled
        self.initialized = False
        self.cleaned_up = False
        self.process_called = False
        
    async def initialize(self):
        self.initialized = True
        
    async def process(self, query, results, context):
        self.process_called = True
        # Add strategy name to results
        for result in results:
            result.strategies_applied.append(self.name)
        return results
        
    async def cleanup(self):
        self.cleaned_up = True
        
    def get_name(self):
        return self.name
        
    def is_enabled(self):
        return self.enabled


class TestStrategyManager:
    """Test the StrategyManager class."""
    
    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = MagicMock()
        settings.strategies = MagicMock()
        settings.strategies.use_contextual_embeddings = False
        settings.strategies.use_hybrid_search = False
        settings.strategies.use_reranking = False
        settings.strategies.use_agentic_rag = False
        return settings

    @pytest.fixture
    def mock_context(self):
        """Create mock application context."""
        context = MagicMock(spec=ApplicationContext)
        return context
    
    def test_initialization(self, mock_settings, mock_context):
        """Test StrategyManager initialization."""
        manager = StrategyManager(mock_settings, mock_context)

        assert manager.settings == mock_settings
        assert manager.context == mock_context
        assert manager._strategies == []
        assert manager._strategy_map == {}
    
    @pytest.mark.asyncio
    async def test_initialize_no_strategies(self, mock_settings, mock_context):
        """Test initialization with no strategies enabled."""
        manager = StrategyManager(mock_settings, mock_context)

        await manager.initialize()

        assert len(manager._strategies) == 0
        assert len(manager._strategy_map) == 0
    
    @pytest.mark.asyncio
    async def test_initialize_with_strategies(self, mock_settings, mock_context):
        """Test initialization with strategies enabled."""
        mock_settings.strategies.use_contextual_embeddings = True
        mock_settings.strategies.use_hybrid_search = True

        with patch('src.strategies.manager.ContextualEmbeddingsStrategy') as mock_ce_class:
            with patch('src.strategies.manager.HybridSearchStrategy') as mock_hs_class:
                mock_ce = MockStrategy("contextual_embeddings")
                mock_hs = MockStrategy("hybrid_search")
                mock_ce_class.return_value = mock_ce
                mock_hs_class.return_value = mock_hs

                manager = StrategyManager(mock_settings, mock_context)
                await manager.initialize()

                # Check strategies were created and initialized
                assert len(manager._strategies) == 2
                assert mock_ce.initialized
                assert mock_hs.initialized
                assert "contextual_embeddings" in manager._strategy_map
                assert "hybrid_search" in manager._strategy_map
    
    @pytest.mark.asyncio
    async def test_process_with_strategies(self, mock_settings, mock_context):
        """Test processing with multiple strategies."""
        manager = StrategyManager(mock_settings, mock_context)
        
        # Add mock strategies
        strategy1 = MockStrategy("strategy1")
        strategy2 = MockStrategy("strategy2")
        await strategy1.initialize()
        await strategy2.initialize()
        
        manager._strategies = [strategy1, strategy2]
        manager._strategies_by_name = {
            "strategy1": strategy1,
            "strategy2": strategy2
        }
        
        # Create test results
        results = [
            RAGResult("Title 1", "url1", "content1", 0.9),
            RAGResult("Title 2", "url2", "content2", 0.8)
        ]
        
        # Process results
        processed = await manager.process("test query", results)
        
        # Verify strategies were called
        assert strategy1.process_called
        assert strategy2.process_called
        
        # Verify strategies were applied to results
        for result in processed:
            assert "strategy1" in result.strategies_applied
            assert "strategy2" in result.strategies_applied
    
    @pytest.mark.asyncio
    async def test_process_with_no_strategies(self, mock_settings, mock_context):
        """Test processing with no strategies returns original results."""
        manager = StrategyManager(mock_settings, mock_context)
        
        results = [
            RAGResult("Title", "url", "content", 0.9)
        ]
        
        processed = await manager.process("query", results)
        
        assert processed == results
        assert len(processed[0].strategies_applied) == 0
    
    @pytest.mark.asyncio
    async def test_process_strategy_error_handling(self, mock_settings, mock_context):
        """Test that strategy errors are handled gracefully."""
        manager = StrategyManager(mock_settings, mock_context)
        
        # Create a strategy that raises an error
        class ErrorStrategy(RAGStrategy):
            async def initialize(self):
                pass
            
            async def process(self, query, results, context):
                raise Exception("Strategy error")
            
            async def cleanup(self):
                pass
            
            def get_name(self):
                return "error_strategy"
            
            def is_enabled(self):
                return True
        
        error_strategy = ErrorStrategy()
        good_strategy = MockStrategy("good_strategy")
        
        manager._strategies = [error_strategy, good_strategy]
        
        results = [RAGResult("Title", "url", "content", 0.9)]
        
        # Should not raise, but continue with other strategies
        with patch('src.strategies.manager.logger') as mock_logger:
            processed = await manager.process("query", results)
            
            # Error should be logged
            mock_logger.error.assert_called()
            
            # Good strategy should still be applied
            assert "good_strategy" in processed[0].strategies_applied
            assert "error_strategy" not in processed[0].strategies_applied
    
    @pytest.mark.asyncio
    async def test_cleanup(self, mock_settings, mock_context):
        """Test cleanup of all strategies."""
        manager = StrategyManager(mock_settings, mock_context)
        
        # Add mock strategies
        strategy1 = MockStrategy("strategy1")
        strategy2 = MockStrategy("strategy2")
        
        manager._strategies = [strategy1, strategy2]
        
        await manager.cleanup()
        
        assert strategy1.cleaned_up
        assert strategy2.cleaned_up
    
    @pytest.mark.asyncio
    async def test_cleanup_error_handling(self, mock_settings, mock_context):
        """Test that cleanup continues even if a strategy fails."""
        manager = StrategyManager(mock_settings, mock_context)
        
        # Create a strategy that raises during cleanup
        class ErrorCleanupStrategy(MockStrategy):
            async def cleanup(self):
                raise Exception("Cleanup error")
        
        error_strategy = ErrorCleanupStrategy("error_strategy")
        good_strategy = MockStrategy("good_strategy")
        
        manager._strategies = [error_strategy, good_strategy]
        
        # Should not raise
        with patch('src.strategies.manager.logger') as mock_logger:
            await manager.cleanup()
            
            # Error should be logged
            mock_logger.error.assert_called()
            
            # Good strategy should still be cleaned up
            assert good_strategy.cleaned_up
    
    def test_get_enabled_strategies(self, mock_settings, mock_context):
        """Test getting list of enabled strategies."""
        manager = StrategyManager(mock_settings, mock_context)
        
        # Add mix of enabled and disabled strategies
        enabled1 = MockStrategy("enabled1", enabled=True)
        disabled = MockStrategy("disabled", enabled=False)
        enabled2 = MockStrategy("enabled2", enabled=True)
        
        manager._strategies = [enabled1, disabled, enabled2]
        manager._strategies_by_name = {
            "enabled1": enabled1,
            "disabled": disabled,
            "enabled2": enabled2
        }
        
        enabled_names = manager.get_enabled_strategies()
        
        assert "enabled1" in enabled_names
        assert "enabled2" in enabled_names
        assert "disabled" not in enabled_names
        assert len(enabled_names) == 2
    
    def test_is_strategy_enabled(self, mock_settings, mock_context):
        """Test checking if a specific strategy is enabled."""
        manager = StrategyManager(mock_settings, mock_context)
        
        enabled_strategy = MockStrategy("enabled", enabled=True)
        disabled_strategy = MockStrategy("disabled", enabled=False)
        
        manager._strategies_by_name = {
            "enabled": enabled_strategy,
            "disabled": disabled_strategy
        }
        
        assert manager.is_strategy_enabled("enabled") is True
        assert manager.is_strategy_enabled("disabled") is False
        assert manager.is_strategy_enabled("nonexistent") is False