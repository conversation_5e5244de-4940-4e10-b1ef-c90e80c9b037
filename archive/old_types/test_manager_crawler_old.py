"""Unit tests for the crawler manager module."""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch
from src.crawlers.manager import CrawlerManager
from src.crawlers.base_types import BaseCrawler, URLType
from src.crawlers.base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlError, CrawlType


class MockCrawler(BaseCrawler):
    """Mock crawler for testing."""
    
    def __init__(self, name, can_handle_urls):
        self.name = name
        self.can_handle_urls = can_handle_urls
        self.crawl_called = False
        self.crawled_urls = []
        
    def can_handle(self, url):
        return url in self.can_handle_urls
        
    async def crawl(self, url, options=None):
        self.crawl_called = True
        self.crawled_urls.append(url)
        return CrawlResult(
            url=url,
            content=f"Content from {self.name}",
            title=f"Title from {self.name}"
        )
        
    def get_name(self):
        return self.name


class TestCrawlerManager:
    """Test the CrawlerManager class."""
    
    def test_initialization(self):
        """Test CrawlerManager initialization."""
        manager = CrawlerManager()

        # Check that crawlers dictionary is initialized with all crawler types
        assert isinstance(manager.crawlers, dict)
        assert len(manager.crawlers) == 6  # All CrawlType enum values
        assert manager.smart_crawler is not None
    
    def test_crawler_types_initialized(self):
        """Test that all crawler types are initialized."""
        manager = CrawlerManager()

        # Check that all CrawlType enum values have corresponding crawlers
        from src.crawlers.base_types import CrawlType
        for crawl_type in CrawlType:
            assert crawl_type in manager.crawlers
            assert manager.crawlers[crawl_type] is not None
    
    @pytest.mark.asyncio
    async def test_crawl_single_url(self):
        """Test crawling a single URL."""
        manager = CrawlerManager()

        # Mock the smart crawler to return a successful result
        with patch.object(manager.smart_crawler, 'crawl') as mock_crawl:
            mock_result = CrawlResult(
                success=True,
                url="https://example.com",
                content="Test content",
                crawl_type=CrawlType.SINGLE_PAGE
            )
            mock_crawl.return_value = mock_result

            result = await manager.crawl("https://example.com")

            assert result.success is True
            assert result.url == "https://example.com"
            assert result.content == "Test content"
            mock_crawl.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_crawl_no_matching_crawler(self):
        """Test crawling when crawler returns an error."""
        manager = CrawlerManager()

        # Mock the smart crawler to return an error result
        with patch.object(manager.smart_crawler, 'crawl') as mock_crawl:
            mock_result = CrawlResult(
                success=False,
                url="https://unhandled.com",
                error=CrawlError(error_type="configuration_error", message="No handler available")
            )
            mock_crawl.return_value = mock_result

            result = await manager.crawl("https://unhandled.com")

            assert result.success is False
            assert result.error is not None
            assert result.error.error_type == "configuration_error"
    

    
    @pytest.mark.asyncio
    async def test_crawl_with_options(self):
        """Test passing options to crawler."""
        manager = CrawlerManager()

        # Mock the smart crawler to verify options are passed
        with patch.object(manager.smart_crawler, 'crawl') as mock_crawl:
            mock_result = CrawlResult(
                success=True,
                url="https://example.com",
                content="test content",
                crawl_type=CrawlType.SINGLE_PAGE
            )
            mock_crawl.return_value = mock_result

            options = {"depth": 2, "timeout": 30}
            result = await manager.crawl("https://example.com", **options)

            # Verify the crawl was called with the options
            mock_crawl.assert_called_once_with("https://example.com", depth=2, timeout=30)
            assert result.success is True
    
    @pytest.mark.asyncio
    async def test_crawl_error_handling(self):
        """Test error handling during crawl."""
        manager = CrawlerManager()

        # Mock the smart crawler to raise an exception
        with patch.object(manager.smart_crawler, 'crawl') as mock_crawl:
            mock_crawl.side_effect = Exception("Crawl failed")

            result = await manager.crawl("https://example.com")

            assert result.success is False
            assert result.error is not None
            assert "Crawl failed" in result.error.message
    
    @pytest.mark.asyncio
    async def test_batch_crawl(self):
        """Test batch crawling multiple URLs."""
        manager = CrawlerManager()

        # Mock the batch crawler
        with patch.object(manager.crawlers[CrawlType.BATCH], 'crawl') as mock_crawl:
            mock_results = [
                CrawlResult(success=True, url="https://example1.com", content="Content 1", crawl_type=CrawlType.BATCH),
                CrawlResult(success=True, url="https://example2.com", content="Content 2", crawl_type=CrawlType.BATCH),
                CrawlResult(success=True, url="https://example3.com", content="Content 3", crawl_type=CrawlType.BATCH)
            ]
            mock_crawl.return_value = mock_results

            urls = ["https://example1.com", "https://example2.com", "https://example3.com"]
            results = await manager.crawl_batch(urls)

            assert len(results) == 3
            assert results[0].content == "Content 1"
            assert results[1].content == "Content 2"
            assert results[2].content == "Content 3"
    
    @pytest.mark.asyncio
    async def test_batch_crawl_with_options(self):
        """Test batch crawling with options."""
        manager = CrawlerManager()

        # Mock the batch crawler
        with patch.object(manager.crawlers[CrawlType.BATCH], 'crawl') as mock_crawl:
            mock_result = [CrawlResult(success=True, url="https://example.com", content="Content", crawl_type=CrawlType.BATCH)]
            mock_crawl.return_value = mock_result

            urls = ["https://example.com"]
            results = await manager.crawl_batch(urls, parallel=True)

            assert len(results) == 1
            mock_crawl.assert_called_once_with(urls, parallel=True)
    
    def test_get_crawler_for_url(self):
        """Test finding the right crawler for a URL."""
        manager = CrawlerManager()

        # Test getting crawler for different URL types
        crawler = manager.get_crawler_for_url("https://example.com")
        assert crawler is not None

        # Test sitemap URL
        sitemap_crawler = manager.get_crawler_for_url("https://example.com/sitemap.xml")
        assert sitemap_crawler is not None

        # Test robots.txt URL
        robots_crawler = manager.get_crawler_for_url("https://example.com/robots.txt")
        assert robots_crawler is not None
    
    def test_get_available_crawlers(self):
        """Test getting list of available crawler types."""
        manager = CrawlerManager()

        # Check that all expected crawler types are available
        crawler_types = list(manager.crawlers.keys())

        assert CrawlType.SINGLE_PAGE in crawler_types
        assert CrawlType.BATCH in crawler_types
        assert CrawlType.SITEMAP in crawler_types
        assert CrawlType.ROBOTS_TXT in crawler_types
        assert CrawlType.TEXT_FILE in crawler_types
        assert CrawlType.RECURSIVE in crawler_types
        assert len(crawler_types) == 6
    
    @pytest.mark.asyncio
    async def test_crawl_by_type_webpage(self):
        """Test crawling by URL type - webpage."""
        manager = CrawlerManager()

        # Test crawling with explicit crawler type
        with patch.object(manager.crawlers[CrawlType.SINGLE_PAGE], 'crawl') as mock_crawl:
            mock_result = CrawlResult(
                success=True,
                url="https://example.com",
                content="Webpage content",
                crawl_type=CrawlType.SINGLE_PAGE
            )
            mock_crawl.return_value = mock_result

            result = await manager.crawl("https://example.com", crawler_type=CrawlType.SINGLE_PAGE)

            assert result.success is True
            assert result.content == "Webpage content"
            mock_crawl.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_crawl_by_type_sitemap(self):
        """Test crawling by URL type - sitemap."""
        manager = CrawlerManager()

        # Test crawling with explicit sitemap crawler type
        with patch.object(manager.crawlers[CrawlType.SITEMAP], 'crawl') as mock_crawl:
            mock_result = CrawlResult(
                success=True,
                url="https://example.com/sitemap.xml",
                content="Sitemap content",
                crawl_type=CrawlType.SITEMAP
            )
            mock_crawl.return_value = mock_result

            result = await manager.crawl("https://example.com/sitemap.xml", crawler_type=CrawlType.SITEMAP)

            assert result.success is True
            assert result.content == "Sitemap content"
            mock_crawl.assert_called_once()