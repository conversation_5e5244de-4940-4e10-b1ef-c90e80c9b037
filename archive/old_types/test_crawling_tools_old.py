"""Unit tests for the crawling tools module."""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch
from src.tools.crawling_tools import register_crawling_tools
from src.crawlers.base_types import Crawl<PERSON><PERSON><PERSON>, CrawlError
from src.core.application_context import ApplicationContext


class TestCrawlingTools:
    """Test the crawling tools registration and functionality."""
    
    @pytest.fixture
    def mock_server(self):
        """Create mock MCP server."""
        server = MagicMock()
        server.tool = MagicMock()
        return server
    
    @pytest.fixture
    def mock_context(self):
        """Create mock application context."""
        context = MagicMock(spec=ApplicationContext)
        mock_crawler_manager = MagicMock()
        context.crawler_manager = mock_crawler_manager
        return context
    
    def test_register_crawling_tools(self, mock_server, mock_context):
        """Test that crawling tools are registered correctly."""
        register_crawling_tools(mock_server, mock_context)
        
        # Check that tool decorator was called for each tool
        assert mock_server.tool.call_count == 2
        
        # Get the registered tool names
        tool_calls = mock_server.tool.call_args_list
        tool_names = [call[0][0] for call in tool_calls]
        
        assert "crawl_single_page" in tool_names
        assert "smart_crawl_url" in tool_names
    
    @pytest.mark.asyncio
    async def test_crawl_single_page_success(self, mock_server, mock_context):
        """Test successful single page crawl."""
        # Setup mock crawler manager
        mock_result = CrawlResult(
            url="https://example.com",
            content="Test content",
            title="Test Page",
            metadata={"crawled_at": "2024-01-01"}
        )
        mock_context.crawler_manager.crawl = AsyncMock(return_value=mock_result)
        
        # Register tools and get the handler
        register_crawling_tools(mock_server, mock_context)
        crawl_handler = mock_server.tool.call_args_list[0][0][1]
        
        # Test the handler
        result = await crawl_handler(
            url="https://example.com",
            include_images=True,
            max_depth=2
        )
        
        # Verify crawler was called with correct parameters
        mock_context.crawler_manager.crawl.assert_called_once_with(
            "https://example.com",
            {"include_images": True, "max_depth": 2}
        )
        
        # Verify result format
        assert result["success"] is True
        assert result["content"] == "Test content"
        assert result["title"] == "Test Page"
        assert result["url"] == "https://example.com"
        assert result["metadata"] == {"crawled_at": "2024-01-01"}
    
    @pytest.mark.asyncio
    async def test_crawl_single_page_error(self, mock_server, mock_context):
        """Test single page crawl with error."""
        # Setup mock crawler manager to return error
        mock_error = CrawlError("Failed to connect", "CONNECTION_ERROR")
        mock_result = CrawlResult(
            url="https://example.com",
            content="",
            error=mock_error
        )
        mock_context.crawler_manager.crawl = AsyncMock(return_value=mock_result)
        
        # Register tools and get the handler
        register_crawling_tools(mock_server, mock_context)
        crawl_handler = mock_server.tool.call_args_list[0][0][1]
        
        # Test the handler
        result = await crawl_handler(url="https://example.com")
        
        # Verify error response
        assert result["success"] is False
        assert result["error"] == "Failed to connect"
        assert result["error_type"] == "CONNECTION_ERROR"
        assert result["content"] == ""
    
    @pytest.mark.asyncio
    async def test_crawl_single_page_exception(self, mock_server, mock_context):
        """Test single page crawl with exception."""
        # Setup mock crawler manager to raise exception
        mock_context.crawler_manager.crawl = AsyncMock(
            side_effect=Exception("Unexpected error")
        )
        
        # Register tools and get the handler
        register_crawling_tools(mock_server, mock_context)
        crawl_handler = mock_server.tool.call_args_list[0][0][1]
        
        # Test the handler
        result = await crawl_handler(url="https://example.com")
        
        # Verify error response
        assert result["success"] is False
        assert "Unexpected error" in result["error"]
        assert result["error_type"] == "UNKNOWN"
    
    @pytest.mark.asyncio
    async def test_smart_crawl_url_success(self, mock_server, mock_context):
        """Test successful smart crawl."""
        # Setup mock crawler manager
        mock_result = CrawlResult(
            url="https://example.com/sitemap.xml",
            content="<xml>sitemap content</xml>",
            title="Sitemap",
            metadata={"url_type": "sitemap", "urls_found": 10}
        )
        mock_context.crawler_manager.crawl = AsyncMock(return_value=mock_result)
        
        # Register tools and get the handler
        register_crawling_tools(mock_server, mock_context)
        smart_crawl_handler = mock_server.tool.call_args_list[1][0][1]
        
        # Test the handler
        result = await smart_crawl_handler(
            url="https://example.com/sitemap.xml",
            follow_links=True
        )
        
        # Verify crawler was called
        mock_context.crawler_manager.crawl.assert_called_once_with(
            "https://example.com/sitemap.xml",
            {"follow_links": True}
        )
        
        # Verify result
        assert result["success"] is True
        assert result["content"] == "<xml>sitemap content</xml>"
        assert result["metadata"]["url_type"] == "sitemap"
    
    @pytest.mark.asyncio
    async def test_smart_crawl_url_batch_urls(self, mock_server, mock_context):
        """Test smart crawl with multiple URLs."""
        # Setup mock for batch crawl
        mock_results = [
            CrawlResult(url="https://example.com/1", content="Content 1"),
            CrawlResult(url="https://example.com/2", content="Content 2")
        ]
        mock_context.crawler_manager.batch_crawl = AsyncMock(return_value=mock_results)
        
        # Register tools and get the handler
        register_crawling_tools(mock_server, mock_context)
        smart_crawl_handler = mock_server.tool.call_args_list[1][0][1]
        
        # Test with list of URLs
        result = await smart_crawl_handler(
            url=["https://example.com/1", "https://example.com/2"]
        )
        
        # Verify batch crawl was called
        mock_context.crawler_manager.batch_crawl.assert_called_once()
        
        # Verify result format for batch
        assert result["success"] is True
        assert result["results_count"] == 2
        assert len(result["results"]) == 2
        assert result["results"][0]["content"] == "Content 1"
        assert result["results"][1]["content"] == "Content 2"
    
    @pytest.mark.asyncio
    async def test_smart_crawl_url_error(self, mock_server, mock_context):
        """Test smart crawl with error."""
        # Setup mock crawler manager to return error
        mock_error = CrawlError("Invalid URL", "INVALID_URL")
        mock_result = CrawlResult(
            url="https://invalid",
            content="",
            error=mock_error
        )
        mock_context.crawler_manager.crawl = AsyncMock(return_value=mock_result)
        
        # Register tools and get the handler
        register_crawling_tools(mock_server, mock_context)
        smart_crawl_handler = mock_server.tool.call_args_list[1][0][1]
        
        # Test the handler
        result = await smart_crawl_handler(url="https://invalid")
        
        # Verify error response
        assert result["success"] is False
        assert result["error"] == "Invalid URL"
        assert result["error_type"] == "INVALID_URL"
    
    def test_tool_descriptions(self, mock_server, mock_context):
        """Test that tools have proper descriptions."""
        register_crawling_tools(mock_server, mock_context)
        
        # Check tool descriptions from decorator calls
        tool_calls = mock_server.tool.call_args_list
        
        # Check crawl_single_page description
        crawl_single_desc = tool_calls[0][1].get('description', '')
        assert 'crawl' in crawl_single_desc.lower()
        assert 'single' in crawl_single_desc.lower()
        
        # Check smart_crawl_url description  
        smart_crawl_desc = tool_calls[1][1].get('description', '')
        assert 'smart' in smart_crawl_desc.lower() or 'intelligent' in smart_crawl_desc.lower()