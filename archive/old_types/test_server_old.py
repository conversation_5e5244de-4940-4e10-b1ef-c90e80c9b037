"""Unit tests for the server module."""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from src.core.modular_server import create_server
from src.config.settings import Settings
from src.core.application_context import ApplicationContext


class TestServerCreation:
    """Test the server creation functionality."""
    
    @pytest.fixture
    def mock_settings(self):
        """Create mock settings for testing."""
        settings = MagicMock(spec=Settings)

        # Mock server settings
        settings.server = MagicMock()
        settings.server.host = "0.0.0.0"
        settings.server.port = 8051

        # Mock database settings
        settings.database = MagicMock()
        settings.database.url = "https://test.supabase.co"
        settings.database.service_key = MagicMock()
        settings.database.service_key.get_secret_value.return_value = "test-key"

        # Mock AI settings
        settings.ai = MagicMock()
        settings.ai.openai_api_key = MagicMock()
        settings.ai.openai_api_key.get_secret_value.return_value = "test-openai"

        return settings
    
    @pytest.fixture
    def mock_context(self, mock_settings):
        """Create mock application context."""
        return MagicMock(spec=ApplicationContext)
    
    @patch('src.core.server.Server')
    @patch('src.core.server.ApplicationContext')
    @patch('src.core.server.register_all_tools')
    def test_create_server_basic(self, mock_register_tools, mock_context_class, mock_server_class, mock_settings):
        """Test basic server creation."""
        # Setup mocks
        mock_context = MagicMock()
        mock_context_class.return_value = mock_context
        mock_server_instance = MagicMock()
        mock_server_class.return_value = mock_server_instance
        
        # Create server
        server = create_server(mock_settings)
        
        # Verify calls
        mock_context_class.assert_called_once_with(mock_settings)
        mock_server_class.assert_called_once_with("crawl4ai-mcp")
        mock_register_tools.assert_called_once_with(mock_server_instance, mock_context)
        
        assert server == mock_server_instance
    
    @patch('src.core.server.Server')
    @patch('src.core.server.ApplicationContext')
    @patch('src.core.server.register_all_tools')
    def test_create_server_with_custom_context(self, mock_register_tools, mock_context_class, mock_server_class, mock_settings, mock_context):
        """Test server creation with custom context."""
        # Setup mocks
        mock_server_instance = MagicMock()
        mock_server_class.return_value = mock_server_instance
        
        # Create server with custom context
        server = create_server(mock_settings, mock_context)
        
        # Verify context class was not called
        mock_context_class.assert_not_called()
        
        # Verify server creation and tool registration
        mock_server_class.assert_called_once_with("crawl4ai-mcp")
        mock_register_tools.assert_called_once_with(mock_server_instance, mock_context)
        
        assert server == mock_server_instance
    
    @patch('src.core.server.Server')
    def test_server_lifecycle_setup(self, mock_server_class, mock_settings):
        """Test that server lifecycle handlers are set up correctly."""
        # Setup mocks
        mock_server_instance = MagicMock()
        mock_server_class.return_value = mock_server_instance
        
        with patch('src.core.server.ApplicationContext') as mock_context_class:
            mock_context = MagicMock()
            mock_context.initialize = AsyncMock()
            mock_context.cleanup = AsyncMock()
            mock_context_class.return_value = mock_context
            
            with patch('src.core.server.register_all_tools'):
                # Create server
                server = create_server(mock_settings)
                
                # Check that lifecycle handlers were registered
                assert mock_server_instance.on_ready.called
                assert mock_server_instance.on_shutdown.called
    
    @pytest.mark.asyncio
    async def test_on_ready_handler(self, mock_settings):
        """Test the on_ready handler initializes context."""
        with patch('src.core.server.Server') as mock_server_class:
            mock_server_instance = MagicMock()
            mock_server_class.return_value = mock_server_instance
            
            with patch('src.core.server.ApplicationContext') as mock_context_class:
                mock_context = MagicMock()
                mock_context.initialize = AsyncMock()
                mock_context_class.return_value = mock_context
                
                with patch('src.core.server.register_all_tools'):
                    # Create server
                    create_server(mock_settings)
                    
                    # Get the on_ready handler
                    on_ready_call = mock_server_instance.on_ready.call_args
                    on_ready_handler = on_ready_call[0][0]
                    
                    # Call the handler
                    await on_ready_handler()
                    
                    # Verify context was initialized
                    mock_context.initialize.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_on_shutdown_handler(self, mock_settings):
        """Test the on_shutdown handler cleans up context."""
        with patch('src.core.server.Server') as mock_server_class:
            mock_server_instance = MagicMock()
            mock_server_class.return_value = mock_server_instance
            
            with patch('src.core.server.ApplicationContext') as mock_context_class:
                mock_context = MagicMock()
                mock_context.cleanup = AsyncMock()
                mock_context_class.return_value = mock_context
                
                with patch('src.core.server.register_all_tools'):
                    # Create server
                    create_server(mock_settings)
                    
                    # Get the on_shutdown handler
                    on_shutdown_call = mock_server_instance.on_shutdown.call_args
                    on_shutdown_handler = on_shutdown_call[0][0]
                    
                    # Call the handler
                    await on_shutdown_handler()
                    
                    # Verify context was cleaned up
                    mock_context.cleanup.assert_called_once()
    
    @patch('src.core.server.Server')
    @patch('src.core.server.ApplicationContext')
    @patch('src.core.server.register_all_tools')
    def test_tool_registration_error_handling(self, mock_register_tools, mock_context_class, mock_server_class, mock_settings):
        """Test error handling during tool registration."""
        # Setup mocks
        mock_context = MagicMock()
        mock_context_class.return_value = mock_context
        mock_server_instance = MagicMock()
        mock_server_class.return_value = mock_server_instance
        
        # Make register_all_tools raise an exception
        mock_register_tools.side_effect = Exception("Tool registration failed")
        
        # Server creation should still succeed but propagate the error
        with pytest.raises(Exception, match="Tool registration failed"):
            create_server(mock_settings)
        
        # Verify server and context were still created
        mock_context_class.assert_called_once_with(mock_settings)
        mock_server_class.assert_called_once_with("crawl4ai-mcp")