"""Plugin-based configuration system with dynamic loading and hot-reload."""

import os
import json
import yaml
import toml
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Type, Callable
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileModifiedEvent
import importlib.util
import threading
import hashlib

@dataclass
class ConfigSchema:
    """Schema definition for configuration validation."""
    name: str
    version: str
    fields: Dict[str, Dict[str, Any]]
    required: List[str] = field(default_factory=list)
    
    def validate(self, config: Dict[str, Any]) -> List[str]:
        """Validate configuration against schema."""
        errors = []
        
        # Check required fields
        for req in self.required:
            if req not in config:
                errors.append(f"Missing required field: {req}")
        
        # Validate field types and constraints
        for field_name, field_spec in self.fields.items():
            if field_name in config:
                value = config[field_name]
                expected_type = field_spec.get("type")
                
                if expected_type and not isinstance(value, expected_type):
                    errors.append(f"Field '{field_name}' must be of type {expected_type.__name__}")
                
                # Check constraints
                if "min" in field_spec and value < field_spec["min"]:
                    errors.append(f"Field '{field_name}' must be >= {field_spec['min']}")
                if "max" in field_spec and value > field_spec["max"]:
                    errors.append(f"Field '{field_name}' must be <= {field_spec['max']}")
                if "choices" in field_spec and value not in field_spec["choices"]:
                    errors.append(f"Field '{field_name}' must be one of {field_spec['choices']}")
        
        return errors

class ConfigPlugin(ABC):
    """Base class for configuration plugins."""
    
    @abstractmethod
    def get_name(self) -> str:
        """Get plugin name."""
        pass
    
    @abstractmethod
    def get_schema(self) -> ConfigSchema:
        """Get configuration schema."""
        pass
    
    @abstractmethod
    def transform(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Transform configuration."""
        pass
    
    @abstractmethod
    def validate(self, config: Dict[str, Any]) -> List[str]:
        """Validate configuration."""
        pass

class ConfigLoader(ABC):
    """Base class for configuration loaders."""
    
    @abstractmethod
    def can_load(self, path: Path) -> bool:
        """Check if this loader can handle the file."""
        pass
    
    @abstractmethod
    def load(self, path: Path) -> Dict[str, Any]:
        """Load configuration from file."""
        pass

class YAMLConfigLoader(ConfigLoader):
    """YAML configuration loader."""
    
    def can_load(self, path: Path) -> bool:
        return path.suffix in ['.yaml', '.yml']
    
    def load(self, path: Path) -> Dict[str, Any]:
        with open(path, 'r') as f:
            return yaml.safe_load(f)

class JSONConfigLoader(ConfigLoader):
    """JSON configuration loader."""
    
    def can_load(self, path: Path) -> bool:
        return path.suffix == '.json'
    
    def load(self, path: Path) -> Dict[str, Any]:
        with open(path, 'r') as f:
            return json.load(f)

class TOMLConfigLoader(ConfigLoader):
    """TOML configuration loader."""
    
    def can_load(self, path: Path) -> bool:
        return path.suffix == '.toml'
    
    def load(self, path: Path) -> Dict[str, Any]:
        with open(path, 'r') as f:
            return toml.load(f)

class ConfigFileWatcher(FileSystemEventHandler):
    """Watch configuration files for changes."""
    
    def __init__(self, callback: Callable[[Path], None]):
        self.callback = callback
        self._file_hashes: Dict[str, str] = {}
    
    def on_modified(self, event: FileModifiedEvent):
        if not event.is_directory:
            path = Path(event.src_path)
            
            # Check if file content actually changed
            new_hash = self._calculate_hash(path)
            old_hash = self._file_hashes.get(str(path))
            
            if new_hash != old_hash:
                self._file_hashes[str(path)] = new_hash
                self.callback(path)
    
    def _calculate_hash(self, path: Path) -> str:
        """Calculate file hash."""
        if path.exists():
            return hashlib.md5(path.read_bytes()).hexdigest()
        return ""

@dataclass
class PluginSettings:
    """Plugin-based settings with dynamic loading."""
    
    # Core configuration
    _config: Dict[str, Any] = field(default_factory=dict)
    
    # Plugin registry
    _plugins: Dict[str, ConfigPlugin] = field(default_factory=dict)
    
    # Loader registry
    _loaders: List[ConfigLoader] = field(default_factory=list)
    
    # Configuration sources
    _config_files: List[Path] = field(default_factory=list)
    
    # Hot reload
    _enable_hot_reload: bool = field(default=True)
    _observer: Optional[Observer] = field(default=None, init=False)
    
    # Callbacks
    _change_callbacks: List[Callable[[Dict[str, Any]], None]] = field(default_factory=list)
    
    def __post_init__(self):
        """Initialize default loaders and start watching."""
        # Register default loaders
        self.register_loader(YAMLConfigLoader())
        self.register_loader(JSONConfigLoader())
        self.register_loader(TOMLConfigLoader())
        
        # Load configuration
        self._load_all_configs()
        
        # Start file watching if enabled
        if self._enable_hot_reload:
            self._start_watching()
    
    def register_plugin(self, plugin: ConfigPlugin) -> None:
        """Register a configuration plugin."""
        self._plugins[plugin.get_name()] = plugin
    
    def register_loader(self, loader: ConfigLoader) -> None:
        """Register a configuration loader."""
        self._loaders.append(loader)
    
    def add_config_file(self, path: Path) -> None:
        """Add a configuration file to watch."""
        if path.exists():
            self._config_files.append(path)
            self._load_config_file(path)
    
    def on_change(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Register a callback for configuration changes."""
        self._change_callbacks.append(callback)
    
    def _load_all_configs(self) -> None:
        """Load all configuration files."""
        # Default paths to check
        default_paths = [
            Path("crawl4ai.yaml"),
            Path("config/settings.yaml"),
            Path(".crawl4ai/config.yaml"),
            Path.home() / ".crawl4ai" / "config.yaml",
        ]
        
        # Environment-specific configs
        env = os.environ.get("CRAWL4AI_ENV", "development")
        default_paths.extend([
            Path(f"config/{env}.yaml"),
            Path(f"crawl4ai.{env}.yaml"),
        ])
        
        for path in default_paths:
            if path.exists():
                self.add_config_file(path)
    
    def _load_config_file(self, path: Path) -> None:
        """Load a single configuration file."""
        for loader in self._loaders:
            if loader.can_load(path):
                try:
                    config = loader.load(path)
                    self._merge_config(config)
                    self._apply_plugins(config)
                    break
                except Exception as e:
                    print(f"Error loading {path}: {e}")
    
    def _merge_config(self, new_config: Dict[str, Any]) -> None:
        """Merge new configuration with existing."""
        # Deep merge logic
        def deep_merge(base: Dict, update: Dict) -> Dict:
            for key, value in update.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    deep_merge(base[key], value)
                else:
                    base[key] = value
            return base
        
        deep_merge(self._config, new_config)
    
    def _apply_plugins(self, config: Dict[str, Any]) -> None:
        """Apply all registered plugins to configuration."""
        for plugin in self._plugins.values():
            # Validate with plugin schema
            errors = plugin.validate(config)
            if errors:
                print(f"Plugin {plugin.get_name()} validation errors: {errors}")
                continue
            
            # Transform configuration
            config = plugin.transform(config)
    
    def _start_watching(self) -> None:
        """Start watching configuration files for changes."""
        if not self._config_files:
            return
        
        self._observer = Observer()
        handler = ConfigFileWatcher(self._on_file_changed)
        
        # Watch all parent directories
        watched_dirs = set()
        for path in self._config_files:
            parent = path.parent
            if parent not in watched_dirs:
                self._observer.schedule(handler, str(parent), recursive=False)
                watched_dirs.add(parent)
        
        self._observer.start()
    
    def _on_file_changed(self, path: Path) -> None:
        """Handle configuration file changes."""
        if path in self._config_files:
            print(f"Configuration file changed: {path}")
            
            # Reload configuration
            old_config = self._config.copy()
            self._config.clear()
            self._load_all_configs()
            
            # Notify callbacks if configuration changed
            if self._config != old_config:
                for callback in self._change_callbacks:
                    callback(self._config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with dot notation support."""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value with dot notation support."""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def load_plugin_from_file(self, path: Path) -> None:
        """Dynamically load a plugin from a Python file."""
        spec = importlib.util.spec_from_file_location("config_plugin", path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Find and register plugin classes
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if (isinstance(attr, type) and 
                issubclass(attr, ConfigPlugin) and 
                attr is not ConfigPlugin):
                plugin = attr()
                self.register_plugin(plugin)
    
    def get_schema(self) -> Dict[str, ConfigSchema]:
        """Get all registered schemas."""
        return {
            name: plugin.get_schema() 
            for name, plugin in self._plugins.items()
        }
    
    def validate_all(self) -> Dict[str, List[str]]:
        """Validate configuration against all plugins."""
        all_errors = {}
        
        for name, plugin in self._plugins.items():
            errors = plugin.validate(self._config)
            if errors:
                all_errors[name] = errors
        
        return all_errors
    
    def export_schema(self, format: str = "json") -> str:
        """Export configuration schema."""
        schemas = self.get_schema()
        
        if format == "json":
            return json.dumps(schemas, indent=2, default=str)
        elif format == "yaml":
            return yaml.dump(schemas, default_flow_style=False)
        else:
            raise ValueError(f"Unsupported format: {format}")
    
    def __del__(self):
        """Cleanup file watcher."""
        if self._observer and self._observer.is_alive():
            self._observer.stop()
            self._observer.join()

def load_settings() -> PluginSettings:
    """Load settings with plugin architecture."""
    settings = PluginSettings()
    
    # Auto-discover and load plugins
    plugin_dir = Path("config/plugins")
    if plugin_dir.exists():
        for plugin_file in plugin_dir.glob("*.py"):
            try:
                settings.load_plugin_from_file(plugin_file)
            except Exception as e:
                print(f"Failed to load plugin {plugin_file}: {e}")
    
    return settings